# -*- coding: utf-8 -*-
"""
QS Top100 大学排名爬虫 - 改进版本
从QS官网爬取真实的2025年Top100大学排名数据
"""

import json
import time
import logging
import re
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSTop100ScraperImproved:
    """QS Top100 大学排名爬虫 - 改进版本"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.topuniversities.com/world-university-rankings"
        self.universities = []

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 使用无头模式提高效率
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 使用当前目录的ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')

            if os.path.exists(chromedriver_path):
                logger.info(f"使用本地ChromeDriver: {chromedriver_path}")
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                logger.error(f"ChromeDriver不存在: {chromedriver_path}")
                return False

            # 隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("Chrome浏览器驱动设置成功")
            return True

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_top100_universities(self):
        """爬取Top100大学排名数据"""
        if not self.setup_driver():
            return []

        try:
            logger.info("正在访问QS排名页面...")
            self.driver.get(self.base_url)

            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            logger.info("页面加载完成，开始提取数据...")
            time.sleep(5)

            # 处理可能的Cookie弹窗
            self._handle_popups()

            # 提取排名数据
            universities = self._extract_university_data()

            if len(universities) < 50:
                logger.info("数据量不足，尝试滚动加载更多...")
                self._scroll_to_load_more()
                universities = self._extract_university_data()

            # 确保只返回Top100
            universities = universities[:100]
            
            logger.info(f"成功提取 {len(universities)} 所大学的数据")
            return universities

        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _handle_popups(self):
        """处理弹窗"""
        try:
            # 处理Cookie弹窗
            cookie_buttons = [
                "button[id*='accept']",
                "button[class*='accept']",
                ".cookie-accept",
                "#accept-cookies"
            ]
            
            for selector in cookie_buttons:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            element.click()
                            logger.info("已处理Cookie弹窗")
                            time.sleep(1)
                            return
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"处理弹窗时出错: {e}")

    def _scroll_to_load_more(self):
        """滚动页面加载更多数据"""
        try:
            for i in range(5):
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                logger.info(f"滚动第 {i+1} 次")
                
        except Exception as e:
            logger.error(f"滚动页面时出错: {e}")

    def _extract_university_data(self):
        """提取大学数据"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            universities = []
            
            # 尝试多种选择器
            selectors = [
                'table tbody tr',
                '.ranking-data tbody tr',
                '.university-list .university-item',
                '.ranking-item',
                '.rankings-table tbody tr',
                'tr[data-rank]',
                'div[data-rank]'
            ]
            
            for selector in selectors:
                rows = soup.select(selector)
                if len(rows) >= 20:  # 确保找到足够的数据
                    logger.info(f"使用选择器 '{selector}' 找到 {len(rows)} 行数据")
                    universities = self._parse_university_rows(rows)
                    if len(universities) >= 20:
                        break
            
            return universities
            
        except Exception as e:
            logger.error(f"提取大学数据时出错: {e}")
            return []

    def _parse_university_rows(self, rows):
        """解析大学数据行"""
        universities = []
        
        for row in rows:
            try:
                university_data = self._extract_university_info(row)
                if university_data:
                    universities.append(university_data)
            except Exception as e:
                logger.debug(f"解析行数据时出错: {e}")
                continue
        
        # 按排名排序并去重
        universities = self._clean_and_sort_data(universities)
        return universities

    def _extract_university_info(self, row):
        """从行中提取大学信息"""
        try:
            text = row.get_text(separator='|', strip=True)
            cells = [cell.strip() for cell in text.split('|') if cell.strip()]
            
            if len(cells) < 2:
                return None
            
            # 提取排名
            rank = self._find_ranking(cells)
            if not rank or rank > 1000:
                return None
            
            # 提取大学名称
            name = self._find_university_name(cells)
            if not name:
                return None
            
            # 提取国家
            country = self._find_country(cells)
            
            return {
                "name": name,
                "qs_ranking": rank,
                "country": country,
                "official_website": ""
            }
            
        except Exception as e:
            logger.debug(f"提取大学信息时出错: {e}")
            return None

    def _find_ranking(self, cells):
        """查找排名"""
        for cell in cells:
            if re.match(r'^\d+$', cell):
                rank = int(cell)
                if 1 <= rank <= 1000:
                    return rank
        return None

    def _find_university_name(self, cells):
        """查找大学名称"""
        for cell in cells:
            if len(cell) > 10 and any(keyword in cell for keyword in 
                ['University', 'College', 'Institute', 'School', 'Academy']):
                return cell
        
        # 如果没有找到包含关键词的，找最长的非数字文本
        for cell in cells:
            if len(cell) > 15 and not re.match(r'^\d+', cell):
                return cell
        
        return None

    def _find_country(self, cells):
        """查找国家"""
        countries = [
            'United States', 'United Kingdom', 'China', 'Germany', 'Australia',
            'Canada', 'Japan', 'France', 'Singapore', 'Switzerland', 'Netherlands',
            'Sweden', 'Denmark', 'Norway', 'South Korea', 'Hong Kong', 'Taiwan',
            'India', 'Malaysia', 'Thailand', 'Indonesia', 'Philippines', 'Vietnam',
            'Brazil', 'Argentina', 'Chile', 'South Africa', 'Egypt', 'Israel'
        ]
        
        for cell in cells:
            for country in countries:
                if country.lower() in cell.lower():
                    return country
        
        return ""

    def _clean_and_sort_data(self, universities):
        """清理和排序数据"""
        # 去重
        seen = set()
        unique_universities = []
        
        for uni in universities:
            key = f"{uni['name']}_{uni['qs_ranking']}"
            if key not in seen:
                seen.add(key)
                unique_universities.append(uni)
        
        # 按排名排序
        unique_universities.sort(key=lambda x: x['qs_ranking'])
        return unique_universities

def main():
    """主函数"""
    logger.info("开始爬取QS Top100大学排名数据...")
    
    scraper = QSTop100ScraperImproved()
    universities = scraper.scrape_top100_universities()
    
    if universities:
        # 处理数据，添加洲际信息
        processor = DataProcessor()
        processed_universities = []
        
        for uni in universities:
            processed_uni = {
                "name": uni['name'],
                "qs_ranking": uni['qs_ranking'],
                "country": uni['country'],
                "continent": processor.get_continent_by_country(uni['country']),
                "official_website": uni.get('official_website', ''),
            }
            processed_universities.append(processed_uni)
        
        # 保存数据
        output_file = "qs_top100_2025_improved.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到 {output_file}")
        
        # 显示前10名
        logger.info("\n前10名大学:")
        for uni in processed_universities[:10]:
            logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']})")
        
        print(f"\n✅ 成功爬取 {len(processed_universities)} 所大学的QS 2025排名数据")
        print(f"📁 数据已保存到: {output_file}")
        
    else:
        logger.error("未能获取到排名数据")
        print("❌ 爬取失败，请检查网络连接")

if __name__ == "__main__":
    main()
