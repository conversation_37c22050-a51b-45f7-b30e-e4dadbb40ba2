# -*- coding: utf-8 -*-
"""
为QS Top100大学数据添加官方网站信息
"""

import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_official_websites():
    """为大学数据添加官方网站"""
    
    # 预定义的官方网站映射
    university_websites = {
        "Massachusetts Institute of Technology": "https://web.mit.edu",
        "Imperial College London": "https://www.imperial.ac.uk",
        "University of Oxford": "https://www.ox.ac.uk",
        "Harvard University": "https://www.harvard.edu",
        "University of Cambridge": "https://www.cam.ac.uk",
        "Stanford University": "https://www.stanford.edu",
        "ETH Zurich": "https://ethz.ch",
        "National University of Singapore": "https://www.nus.edu.sg",
        "University College London": "https://www.ucl.ac.uk",
        "California Institute of Technology": "https://www.caltech.edu",
        "University of Pennsylvania": "https://www.upenn.edu",
        "University of Edinburgh": "https://www.ed.ac.uk",
        "University of Melbourne": "https://www.unimelb.edu.au",
        "Peking University": "https://www.pku.edu.cn",
        "University of Tokyo": "https://www.u-tokyo.ac.jp",
        "King's College London": "https://www.kcl.ac.uk",
        "University of Hong Kong": "https://www.hku.hk",
        "University of Toronto": "https://www.utoronto.ca",
        "University of New South Wales": "https://www.unsw.edu.au",
        "Tsinghua University": "https://www.tsinghua.edu.cn",
        "University of Chicago": "https://www.uchicago.edu",
        "Princeton University": "https://www.princeton.edu",
        "Yale University": "https://www.yale.edu",
        "Nanyang Technological University": "https://www.ntu.edu.sg",
        "École Polytechnique Fédérale de Lausanne": "https://www.epfl.ch",
        "Australian National University": "https://www.anu.edu.au",
        "University of California, Los Angeles": "https://www.ucla.edu",
        "University of Sydney": "https://www.sydney.edu.au",
        "University of Manchester": "https://www.manchester.ac.uk",
        "Seoul National University": "https://www.snu.ac.kr",
        "University of California, Berkeley": "https://www.berkeley.edu",
        "University of Bristol": "https://www.bristol.ac.uk",
        "Technical University of Munich": "https://www.tum.de",
        "University of Michigan": "https://www.umich.edu",
        "London School of Economics and Political Science": "https://www.lse.ac.uk",
        "University of British Columbia": "https://www.ubc.ca",
        "École Normale Supérieure de Paris": "https://www.ens.psl.eu",
        "University of Amsterdam": "https://www.uva.nl",
        "New York University": "https://www.nyu.edu",
        "Kyoto University": "https://www.kyoto-u.ac.jp",
        "Monash University": "https://www.monash.edu",
        "University of Queensland": "https://www.uq.edu.au",
        "McGill University": "https://www.mcgill.ca",
        "Delft University of Technology": "https://www.tudelft.nl",
        "University of Warwick": "https://www.warwick.ac.uk",
        "University of Copenhagen": "https://www.ku.dk",
        "University of Glasgow": "https://www.gla.ac.uk",
        "Brown University": "https://www.brown.edu",
        "University of Wisconsin-Madison": "https://www.wisc.edu",
        "Chinese University of Hong Kong": "https://www.cuhk.edu.hk",
        "University of North Carolina at Chapel Hill": "https://www.unc.edu",
        "London School of Hygiene & Tropical Medicine": "https://www.lshtm.ac.uk",
        "Fudan University": "https://www.fudan.edu.cn",
        "Carnegie Mellon University": "https://www.cmu.edu",
        "University of Birmingham": "https://www.birmingham.ac.uk",
        "University of St Andrews": "https://www.st-andrews.ac.uk",
        "Tokyo Institute of Technology": "https://www.titech.ac.jp",
        "University of Sheffield": "https://www.sheffield.ac.uk",
        "University of Leeds": "https://www.leeds.ac.uk",
        "University of Auckland": "https://www.auckland.ac.nz",
        "University of Nottingham": "https://www.nottingham.ac.uk",
        "Boston University": "https://www.bu.edu",
        "University of Southampton": "https://www.southampton.ac.uk",
        "University of Washington": "https://www.washington.edu",
        "University of Zurich": "https://www.uzh.ch",
        "Trinity College Dublin": "https://www.tcd.ie",
        "University of California, San Diego": "https://www.ucsd.edu",
        "University of Alberta": "https://www.ualberta.ca",
        "KU Leuven": "https://www.kuleuven.be",
        "University of Texas at Austin": "https://www.utexas.edu",
        "Sorbonne University": "https://www.sorbonne-universite.fr",
        "University of Helsinki": "https://www.helsinki.fi",
        "Georgetown University": "https://www.georgetown.edu",
        "University of Groningen": "https://www.rug.nl",
        "Lund University": "https://www.lu.se",
        "Georgia Institute of Technology": "https://www.gatech.edu",
        "University of Oslo": "https://www.uio.no",
        "Pohang University of Science and Technology": "https://www.postech.ac.kr",
        "University of Science and Technology of China": "https://www.ustc.edu.cn",
        "University of Adelaide": "https://www.adelaide.edu.au",
        "University of Illinois at Urbana-Champaign": "https://www.illinois.edu",
        "Durham University": "https://www.durham.ac.uk",
        "Yonsei University": "https://www.yonsei.ac.kr",
        "University of Western Australia": "https://www.uwa.edu.au",
        "Korea Advanced Institute of Science and Technology": "https://www.kaist.ac.kr",
        "University of Bern": "https://www.unibe.ch",
        "University of Rochester": "https://www.rochester.edu",
        "University of York": "https://www.york.ac.uk",
        "University of California, Davis": "https://www.ucdavis.edu",
        "Washington University in St. Louis": "https://www.wustl.edu",
        "University of Exeter": "https://www.exeter.ac.uk",
        "Eindhoven University of Technology": "https://www.tue.nl",
        "University of Bath": "https://www.bath.ac.uk",
        "University of Liverpool": "https://www.liverpool.ac.uk",
        "University of Basel": "https://www.unibas.ch",
        "University of Waterloo": "https://www.uwaterloo.ca",
        "McMaster University": "https://www.mcmaster.ca",
        "University of Geneva": "https://www.unige.ch",
        "Université PSL": "https://www.psl.eu",
        "University of California, Santa Barbara": "https://www.ucsb.edu"
    }
    
    # 读取现有数据
    try:
        with open('qs_top100_2025_final.json', 'r', encoding='utf-8') as f:
            universities = json.load(f)
        
        logger.info(f"读取了 {len(universities)} 所大学的数据")
        
        # 添加官方网站
        updated_count = 0
        for uni in universities:
            name = uni['name']
            if name in university_websites:
                uni['official_website'] = university_websites[name]
                updated_count += 1
        
        logger.info(f"更新了 {updated_count} 所大学的官方网站信息")
        
        # 保存更新后的数据
        with open('qs_top100_2025_final.json', 'w', encoding='utf-8') as f:
            json.dump(universities, f, ensure_ascii=False, indent=2)
        
        logger.info("数据已更新并保存")
        
        # 显示前10名的网站信息
        logger.info("\n前10名大学的官方网站:")
        for uni in universities[:10]:
            website = uni['official_website'] if uni['official_website'] else "未找到"
            logger.info(f"  {uni['qs_ranking']}. {uni['name']}: {website}")
        
        print(f"\n✅ 成功为 {updated_count} 所大学添加了官方网站信息")
        
    except Exception as e:
        logger.error(f"处理数据时出错: {e}")

if __name__ == "__main__":
    add_official_websites()
