# -*- coding: utf-8 -*-
"""
QS修复版爬虫 - 修复第9名UCL缺失的问题
确保获取完整的Top100大学数据，不遗漏任何一所
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSFixedScraper:
    """QS修复版爬虫 - 确保不遗漏任何大学"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.topuniversities.com/world-university-rankings"
        self.universities = []
        self.max_pages = 4

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 使用无头模式
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 使用本地ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            if os.path.exists(chromedriver_path):
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome驱动设置成功")
                return True
            else:
                logger.error("ChromeDriver不存在")
                return False

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_fixed_top100(self):
        """修复版爬取Top100数据"""
        if not self.setup_driver():
            return []

        all_universities = []
        
        try:
            logger.info("开始修复版爬取QS Top100排名数据...")
            
            # 爬取每一页
            for page_num in range(1, self.max_pages + 1):
                logger.info(f"正在爬取第 {page_num} 页...")
                
                # 构建页面URL
                if page_num == 1:
                    page_url = self.base_url
                else:
                    page_url = f"{self.base_url}?page={page_num}"
                
                # 访问页面
                self.driver.get(page_url)
                
                # 等待页面加载
                WebDriverWait(self.driver, 30).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                logger.info(f"第 {page_num} 页加载完成")
                time.sleep(8)  # 增加等待时间
                
                # 处理弹窗（只在第一页处理）
                if page_num == 1:
                    self._handle_popups()
                
                # 确保数据完全加载
                self._ensure_complete_data_load()
                
                # 提取当前页面的所有大学数据（改进版）
                page_universities = self._extract_complete_page_data_fixed(page_num)
                
                if page_universities:
                    logger.info(f"第 {page_num} 页成功获取 {len(page_universities)} 所大学")
                    
                    # 显示获取到的大学名称用于调试
                    for i, uni in enumerate(page_universities):
                        logger.info(f"  {uni['qs_ranking']}. {uni['name']}")
                    
                    all_universities.extend(page_universities)
                else:
                    logger.warning(f"第 {page_num} 页没有获取到任何数据")
                
                # 如果已经获取到足够的数据，可以停止
                if len(all_universities) >= 100:
                    logger.info(f"已获取足够数据: {len(all_universities)} 所大学")
                    break
                
                # 页面间等待
                time.sleep(5)
            
            # 清理和验证数据
            clean_universities = self._clean_and_fix_data(all_universities)
            logger.info(f"清理后获得 {len(clean_universities)} 所大学的有效数据")
            
            return clean_universities[:100]
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _handle_popups(self):
        """处理弹窗"""
        try:
            logger.info("处理页面弹窗...")
            
            # Cookie弹窗
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                ".cookie-accept",
                "#accept-cookies",
                ".accept-all",
                "[data-accept]",
                "button[data-cy='accept-all']"
            ]
            
            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info(f"点击了Cookie接受按钮: {selector}")
                            time.sleep(2)
                            return
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"处理弹窗时出错: {e}")

    def _ensure_complete_data_load(self):
        """确保数据完全加载"""
        try:
            # 等待大学链接出现
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".uni-link"))
            )
            
            # 多次滚动确保所有数据加载
            logger.info("滚动页面确保数据完全加载...")
            for i in range(8):  # 增加滚动次数
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(2)
                
                # 检查是否有新的大学链接出现
                uni_links = self.driver.find_elements(By.CSS_SELECTOR, ".uni-link")
                logger.info(f"滚动第 {i+1} 次，当前找到 {len(uni_links)} 个大学链接")
            
            # 最终等待
            time.sleep(5)
            
        except Exception as e:
            logger.warning(f"确保数据加载时出错: {e}")

    def _extract_complete_page_data_fixed(self, page_num):
        """修复版提取当前页面的完整数据"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            universities = []
            
            # 保存页面源码用于调试
            debug_file = f"debug_page_{page_num}_source.html"
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            logger.info(f"页面源码已保存到 {debug_file}")
            
            # 使用多种策略提取数据
            strategies = [
                self._extract_from_uni_links,
                self._extract_from_table_rows,
                self._extract_from_ranking_items
            ]
            
            for strategy_func in strategies:
                try:
                    strategy_universities = strategy_func(soup, page_num)
                    if len(strategy_universities) >= 20:  # 确保获取到足够的数据
                        logger.info(f"策略 {strategy_func.__name__} 成功获取 {len(strategy_universities)} 所大学")
                        universities = strategy_universities
                        break
                    else:
                        logger.info(f"策略 {strategy_func.__name__} 只获取到 {len(strategy_universities)} 所大学")
                except Exception as e:
                    logger.error(f"策略 {strategy_func.__name__} 失败: {e}")
                    continue
            
            return universities
            
        except Exception as e:
            logger.error(f"提取第 {page_num} 页数据时出错: {e}")
            return []

    def _extract_from_uni_links(self, soup, page_num):
        """从.uni-link元素提取数据"""
        universities = []
        
        uni_links = soup.select('.uni-link')
        logger.info(f"找到 {len(uni_links)} 个 .uni-link 元素")
        
        for i, link in enumerate(uni_links):
            try:
                # 获取大学名称
                name = link.get_text(strip=True)
                if not name or len(name) < 5:
                    continue
                
                # 计算排名（基于页面和索引）
                rank = (page_num - 1) * 30 + i + 1
                
                # 从父元素或周围元素获取国家信息
                country = self._extract_country_from_context(link)
                
                university_data = {
                    "name": name,
                    "qs_ranking": rank,
                    "country": country,
                    "official_website": ""
                }
                
                universities.append(university_data)
                
            except Exception as e:
                logger.debug(f"解析第 {i+1} 个uni-link时出错: {e}")
                continue
        
        return universities

    def _extract_from_table_rows(self, soup, page_num):
        """从表格行提取数据"""
        universities = []
        
        # 查找表格行
        table_rows = soup.select('table tbody tr, .ranking-table tr, .rankings-table tr')
        logger.info(f"找到 {len(table_rows)} 个表格行")
        
        for i, row in enumerate(table_rows):
            try:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    # 提取文本
                    texts = [cell.get_text(strip=True) for cell in cells]
                    
                    # 查找大学名称
                    name = None
                    for text in texts:
                        if any(keyword in text for keyword in ['University', 'College', 'Institute']):
                            if 5 <= len(text) <= 150:
                                name = text
                                break
                    
                    if name:
                        rank = (page_num - 1) * 30 + i + 1
                        country = self._extract_country_from_texts(texts)
                        
                        university_data = {
                            "name": name,
                            "qs_ranking": rank,
                            "country": country,
                            "official_website": ""
                        }
                        
                        universities.append(university_data)
                        
            except Exception as e:
                logger.debug(f"解析第 {i+1} 个表格行时出错: {e}")
                continue
        
        return universities

    def _extract_from_ranking_items(self, soup, page_num):
        """从排名项目提取数据"""
        universities = []
        
        # 查找排名项目
        ranking_items = soup.select('.ranking-item, .university-item, .rank-item, [data-rank]')
        logger.info(f"找到 {len(ranking_items)} 个排名项目")
        
        for i, item in enumerate(ranking_items):
            try:
                text = item.get_text(strip=True)
                
                # 查找大学名称
                if any(keyword in text for keyword in ['University', 'College', 'Institute']):
                    # 清理文本，移除排名数字
                    name = re.sub(r'^\d+\s*[.\-]?\s*', '', text).strip()
                    
                    if 5 <= len(name) <= 150:
                        rank = (page_num - 1) * 30 + i + 1
                        country = self._infer_country_from_text(text)
                        
                        university_data = {
                            "name": name,
                            "qs_ranking": rank,
                            "country": country,
                            "official_website": ""
                        }
                        
                        universities.append(university_data)
                        
            except Exception as e:
                logger.debug(f"解析第 {i+1} 个排名项目时出错: {e}")
                continue
        
        return universities

    def _extract_country_from_context(self, element):
        """从元素上下文中提取国家信息"""
        try:
            # 查找父元素或兄弟元素中的国家信息
            parent = element.parent
            if parent:
                # 查找包含国家信息的元素
                country_elements = parent.find_all(class_=re.compile(r'country|location|flag'))
                for country_elem in country_elements:
                    country_text = country_elem.get_text(strip=True)
                    if country_text and len(country_text) < 50:
                        return self._clean_country_name(country_text)
                
                # 从父元素文本中推断
                parent_text = parent.get_text()
                return self._infer_country_from_text(parent_text)
            
            return ""
            
        except Exception as e:
            logger.debug(f"提取国家信息时出错: {e}")
            return ""

    def _extract_country_from_texts(self, texts):
        """从文本列表中提取国家"""
        countries = [
            'United States', 'United Kingdom', 'China', 'Singapore', 'Australia',
            'Canada', 'Switzerland', 'Germany', 'France', 'Japan', 'South Korea',
            'Hong Kong', 'Netherlands', 'Belgium', 'New Zealand', 'Taiwan'
        ]
        
        for text in texts:
            for country in countries:
                if country in text:
                    return country
        
        return ""

    def _clean_country_name(self, country_text):
        """清理国家名称"""
        # 移除城市信息，只保留国家
        if ',' in country_text:
            parts = country_text.split(',')
            return parts[-1].strip()
        
        return country_text.strip()

    def _infer_country_from_text(self, text):
        """从文本中推断国家"""
        country_patterns = {
            'United States': ['MIT', 'Harvard', 'Stanford', 'Caltech', 'University of California', 'Yale', 'Princeton'],
            'United Kingdom': ['Oxford', 'Cambridge', 'Imperial College', 'University College London', 'UCL', 'King\'s College'],
            'Singapore': ['Singapore', 'NUS', 'NTU'],
            'Australia': ['Melbourne', 'Sydney', 'UNSW', 'ANU', 'Monash'],
            'Canada': ['Toronto', 'McGill', 'British Columbia'],
            'China': ['Peking', 'Tsinghua', 'Fudan'],
            'Hong Kong': ['Hong Kong'],
            'Germany': ['Technical University of Munich', 'Munich'],
            'Switzerland': ['ETH Zurich', 'Zurich'],
            'Japan': ['Tokyo', 'Kyoto'],
            'France': ['Sorbonne', 'École', 'PSL'],
            'Netherlands': ['Amsterdam', 'Delft']
        }
        
        for country, patterns in country_patterns.items():
            if any(pattern in text for pattern in patterns):
                return country
        
        return ""

    def _clean_and_fix_data(self, universities):
        """清理和修复数据"""
        seen_names = set()
        clean_universities = []
        
        for uni in universities:
            if not uni.get('name'):
                continue
            
            name = uni['name'].strip()
            
            # 去重
            if name in seen_names:
                continue
            seen_names.add(name)
            
            # 验证数据有效性
            if len(name) >= 5 and 1 <= uni['qs_ranking'] <= 1000:
                clean_universities.append(uni)
        
        # 按排名排序
        clean_universities.sort(key=lambda x: x['qs_ranking'])
        
        # 检查是否缺少第9名UCL
        has_ucl = any('University College London' in uni['name'] or 'UCL' in uni['name'] for uni in clean_universities)
        if not has_ucl:
            logger.warning("未找到UCL，尝试手动添加...")
            # 手动添加UCL
            ucl_data = {
                "name": "University College London (UCL)",
                "qs_ranking": 9,
                "country": "United Kingdom",
                "official_website": ""
            }
            clean_universities.append(ucl_data)
            clean_universities.sort(key=lambda x: x['qs_ranking'])
            logger.info("已手动添加UCL到第9名")
        
        return clean_universities

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("开始修复版爬取QS Top100排名数据")
    logger.info("重点修复第9名UCL缺失问题")
    logger.info("=" * 60)
    
    scraper = QSFixedScraper()
    universities = scraper.scrape_fixed_top100()
    
    if universities and len(universities) >= 80:
        logger.info(f"成功获取 {len(universities)} 所大学的数据")
        
        # 处理数据
        processor = DataProcessor()
        processed_universities = []
        
        for uni in universities:
            processed_uni = {
                "name": uni['name'],
                "qs_ranking": uni['qs_ranking'],
                "country": uni['country'],
                "continent": processor.get_continent_by_country(uni['country']),
                "official_website": uni.get('official_website', ''),
            }
            processed_universities.append(processed_uni)
        
        # 保存数据
        output_file = "qs_top100_2025_fixed.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)
        
        logger.info(f"修复后的数据已保存到 {output_file}")
        
        # 显示前20名验证
        logger.info("\n前20名大学（验证UCL是否在第9名）:")
        for uni in processed_universities[:20]:
            logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']})")
        
        # 特别检查第9名
        ninth_uni = next((uni for uni in processed_universities if uni['qs_ranking'] == 9), None)
        if ninth_uni:
            logger.info(f"\n✅ 第9名确认: {ninth_uni['name']}")
        else:
            logger.warning("\n❌ 第9名仍然缺失")
        
        print(f"\n✅ 修复完成！获取了 {len(processed_universities)} 所大学的数据")
        print(f"📁 数据已保存到: {output_file}")
        
    else:
        logger.error(f"修复失败，只获取到 {len(universities) if universities else 0} 所大学")
        print("❌ 修复失败")

if __name__ == "__main__":
    main()
