# -*- coding: utf-8 -*-
"""
分析第一页的所有30个.uni-link元素
找出为什么UCL没有被正确提取
"""

import json
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FirstPageAnalyzer:
    """第一页分析器"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.topuniversities.com/world-university-rankings"

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            if os.path.exists(chromedriver_path):
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome驱动设置成功")
                return True
            else:
                logger.error("ChromeDriver不存在")
                return False

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def analyze_first_page(self):
        """分析第一页的所有链接"""
        if not self.setup_driver():
            return

        try:
            logger.info("开始分析第一页的所有.uni-link元素...")
            
            # 访问第一页
            self.driver.get(self.base_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            time.sleep(8)
            
            # 处理弹窗
            self._handle_popups()
            
            # 确保数据加载
            self._ensure_data_loaded()
            
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 获取所有.uni-link元素
            uni_links = soup.select('.uni-link')
            logger.info(f"找到 {len(uni_links)} 个 .uni-link 元素")
            
            # 分析每个链接
            all_links_data = []
            for i, link in enumerate(uni_links):
                try:
                    # 获取链接文本
                    link_text = link.get_text(strip=True)
                    
                    # 获取链接的href属性
                    href = link.get('href', '')
                    
                    # 获取父元素的完整文本
                    parent = link.parent
                    parent_text = parent.get_text(strip=True) if parent else ''
                    
                    # 获取更大范围的上下文
                    grandparent = parent.parent if parent and parent.parent else None
                    context_text = grandparent.get_text(strip=True) if grandparent else ''
                    
                    link_data = {
                        "index": i + 1,
                        "link_text": link_text,
                        "href": href,
                        "parent_text": parent_text[:200],  # 限制长度
                        "context_text": context_text[:300]  # 限制长度
                    }
                    
                    all_links_data.append(link_data)
                    
                    # 实时显示
                    logger.info(f"链接 {i+1}: {link_text}")
                    
                    # 特别标记可能是UCL的链接
                    if 'UCL' in link_text or 'University College London' in link_text or 'College London' in link_text:
                        logger.info(f"  ⭐ 可能是UCL: {link_text}")
                    
                except Exception as e:
                    logger.error(f"分析第 {i+1} 个链接时出错: {e}")
                    continue
            
            # 保存详细分析结果
            with open('first_page_links_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(all_links_data, f, ensure_ascii=False, indent=2)
            
            logger.info("详细分析结果已保存到 first_page_links_analysis.json")
            
            # 专门搜索UCL相关内容
            self._search_ucl_in_page(soup)
            
            # 分析排名结构
            self._analyze_ranking_structure(soup)
            
        except Exception as e:
            logger.error(f"分析过程中出错: {e}")
        finally:
            if self.driver:
                self.driver.quit()

    def _handle_popups(self):
        """处理弹窗"""
        try:
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                ".cookie-accept",
                "#accept-cookies"
            ]
            
            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info("处理了弹窗")
                            time.sleep(2)
                            return
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"处理弹窗时出错: {e}")

    def _ensure_data_loaded(self):
        """确保数据完全加载"""
        try:
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".uni-link"))
            )
            
            # 滚动页面
            for i in range(5):
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(2)
            
            time.sleep(5)
            
        except Exception as e:
            logger.warning(f"确保数据加载时出错: {e}")

    def _search_ucl_in_page(self, soup):
        """在页面中搜索UCL相关内容"""
        logger.info("\n=== 搜索UCL相关内容 ===")
        
        page_text = soup.get_text()
        
        # 搜索各种UCL相关关键词
        ucl_keywords = [
            'University College London',
            'UCL',
            'College London',
            'London UCL',
            'U.C.L',
            'university-college-london'
        ]
        
        for keyword in ucl_keywords:
            if keyword.lower() in page_text.lower():
                logger.info(f"✅ 在页面中找到关键词: {keyword}")
                
                # 查找包含该关键词的所有元素
                elements = soup.find_all(text=lambda text: text and keyword.lower() in text.lower())
                for i, elem in enumerate(elements[:3]):  # 只显示前3个
                    parent = elem.parent if elem.parent else elem
                    logger.info(f"  元素 {i+1}: {parent.name} - {elem.strip()[:100]}")
            else:
                logger.warning(f"❌ 未在页面中找到关键词: {keyword}")

    def _analyze_ranking_structure(self, soup):
        """分析排名结构，特别关注第9名"""
        logger.info("\n=== 分析排名结构（关注第9名）===")
        
        # 查找所有包含数字9的元素
        elements_with_9 = soup.find_all(text=lambda text: text and '9' in text)
        
        logger.info(f"找到 {len(elements_with_9)} 个包含数字9的元素:")
        for i, elem in enumerate(elements_with_9[:10]):  # 只显示前10个
            parent = elem.parent if elem.parent else elem
            context = parent.get_text(strip=True)[:150]
            logger.info(f"  {i+1}. {context}")
        
        # 查找可能的排名表格或列表
        ranking_containers = soup.select('table, .ranking-list, .university-list, [data-rank]')
        logger.info(f"\n找到 {len(ranking_containers)} 个可能的排名容器")
        
        for i, container in enumerate(ranking_containers[:3]):
            logger.info(f"容器 {i+1}: {container.name} - {container.get('class', [])}")
            # 在容器中查找第9名
            container_text = container.get_text()
            if '9' in container_text and any(keyword in container_text.lower() for keyword in ['university', 'college']):
                logger.info(f"  可能包含第9名的容器: {container_text[:200]}")

def main():
    """主函数"""
    logger.info("开始分析第一页的所有.uni-link元素...")
    
    analyzer = FirstPageAnalyzer()
    analyzer.analyze_first_page()
    
    print("\n分析完成！请查看生成的 first_page_links_analysis.json 文件。")

if __name__ == "__main__":
    main()
