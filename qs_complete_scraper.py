# -*- coding: utf-8 -*-
"""
QS完整爬虫 - 确保获取完整的Top100大学数据
每页30所大学，总共4页获取120所，取前100所
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSCompleteScraper:
    """QS完整爬虫 - 确保获取Top100数据"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.topuniversities.com/world-university-rankings"
        self.universities = []
        self.target_per_page = 30  # 每页目标30所大学
        self.max_pages = 4  # 最多4页确保覆盖Top100

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 使用无头模式提高效率
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 禁用图片加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 使用本地ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            if os.path.exists(chromedriver_path):
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome驱动设置成功")
                return True
            else:
                logger.error("ChromeDriver不存在")
                return False

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_complete_top100(self):
        """爬取完整的Top100数据"""
        if not self.setup_driver():
            return []

        all_universities = []
        
        try:
            logger.info("开始爬取完整的QS Top100排名数据...")
            
            # 爬取每一页
            for page_num in range(1, self.max_pages + 1):
                logger.info(f"正在爬取第 {page_num} 页...")
                
                # 构建页面URL
                if page_num == 1:
                    page_url = self.base_url
                else:
                    page_url = f"{self.base_url}?page={page_num}"
                
                # 访问页面
                self.driver.get(page_url)
                
                # 等待页面加载
                WebDriverWait(self.driver, 30).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                logger.info(f"第 {page_num} 页加载完成")
                time.sleep(5)
                
                # 处理弹窗（只在第一页处理）
                if page_num == 1:
                    self._handle_popups()
                
                # 等待数据完全加载
                self._wait_for_complete_data_load()
                
                # 提取当前页面的所有大学数据
                page_universities = self._extract_complete_page_data(page_num)
                
                if page_universities:
                    logger.info(f"第 {page_num} 页成功获取 {len(page_universities)} 所大学")
                    all_universities.extend(page_universities)
                    
                    # 检查是否达到每页30所的目标
                    if len(page_universities) < self.target_per_page:
                        logger.warning(f"第 {page_num} 页只获取到 {len(page_universities)} 所大学，少于目标 {self.target_per_page} 所")
                else:
                    logger.warning(f"第 {page_num} 页没有获取到任何数据")
                
                # 如果已经获取到足够的数据，可以停止
                if len(all_universities) >= 100:
                    logger.info(f"已获取足够数据: {len(all_universities)} 所大学")
                    break
                
                # 页面间等待
                time.sleep(3)
            
            # 清理和验证数据
            clean_universities = self._clean_and_validate_data(all_universities)
            logger.info(f"清理后获得 {len(clean_universities)} 所大学的有效数据")
            
            return clean_universities[:100]  # 确保只返回Top100
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _handle_popups(self):
        """处理弹窗"""
        try:
            logger.info("处理页面弹窗...")
            
            # Cookie弹窗
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                ".cookie-accept",
                "#accept-cookies",
                ".accept-all",
                "[data-accept]",
                "button[data-cy='accept-all']"
            ]
            
            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info(f"点击了Cookie接受按钮: {selector}")
                            time.sleep(2)
                            return
                except:
                    continue
            
            # 其他弹窗关闭按钮
            close_selectors = [
                "button[aria-label='Close']",
                ".close",
                ".modal-close",
                "[data-dismiss='modal']"
            ]
            
            for selector in close_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info(f"关闭了弹窗: {selector}")
                            time.sleep(1)
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"处理弹窗时出错: {e}")

    def _wait_for_complete_data_load(self):
        """等待数据完全加载"""
        try:
            # 等待大学链接出现
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".uni-link"))
            )
            
            # 滚动页面确保所有数据加载
            logger.info("滚动页面确保数据完全加载...")
            for i in range(5):
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(1)
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(1)
            
            # 额外等待确保JavaScript渲染完成
            time.sleep(3)
            
        except Exception as e:
            logger.warning(f"等待数据加载时出错: {e}")

    def _extract_complete_page_data(self, page_num):
        """提取当前页面的完整数据"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            universities = []
            
            # 使用最准确的选择器
            uni_links = soup.select('.uni-link')
            logger.info(f"找到 {len(uni_links)} 个 .uni-link 元素")
            
            if len(uni_links) >= 20:  # 确保找到足够的元素
                for i, link in enumerate(uni_links):
                    try:
                        university_data = self._parse_university_link(link, page_num, i + 1)
                        if university_data:
                            universities.append(university_data)
                    except Exception as e:
                        logger.debug(f"解析第 {i+1} 个大学链接时出错: {e}")
                        continue
            else:
                # 如果.uni-link不够，尝试其他选择器
                logger.warning(f".uni-link 元素不足，尝试其他选择器...")
                universities = self._try_alternative_selectors(soup, page_num)
            
            return universities
            
        except Exception as e:
            logger.error(f"提取第 {page_num} 页数据时出错: {e}")
            return []

    def _parse_university_link(self, link_element, page_num, index):
        """解析大学链接元素"""
        try:
            # 获取大学名称
            name = link_element.get_text(strip=True)
            if not name or len(name) < 5:
                return None
            
            # 计算排名（基于页面和索引）
            rank = (page_num - 1) * 30 + index
            
            # 尝试从父元素或兄弟元素中获取国家信息
            country = self._extract_country_from_context(link_element)
            
            return {
                "name": name,
                "qs_ranking": rank,
                "country": country,
                "official_website": ""
            }
            
        except Exception as e:
            logger.debug(f"解析大学链接时出错: {e}")
            return None

    def _extract_country_from_context(self, element):
        """从元素上下文中提取国家信息"""
        try:
            # 查找父元素或兄弟元素中的国家信息
            parent = element.parent
            if parent:
                # 查找包含国家信息的元素
                country_elements = parent.find_all(class_=re.compile(r'country|location|flag'))
                for country_elem in country_elements:
                    country_text = country_elem.get_text(strip=True)
                    if country_text and len(country_text) < 50:
                        return self._normalize_country_name(country_text)
                
                # 如果没有找到专门的国家元素，从文本中推断
                parent_text = parent.get_text()
                return self._infer_country_from_text(parent_text)
            
            return ""
            
        except Exception as e:
            logger.debug(f"提取国家信息时出错: {e}")
            return ""

    def _try_alternative_selectors(self, soup, page_num):
        """尝试其他选择器"""
        universities = []
        
        alternative_selectors = [
            'table tbody tr',
            '.ranking-item',
            '.university-item',
            '[data-university]',
            '.ranking-data tr'
        ]
        
        for selector in alternative_selectors:
            elements = soup.select(selector)
            if len(elements) >= 20:
                logger.info(f"使用备用选择器 '{selector}' 找到 {len(elements)} 个元素")
                
                for i, element in enumerate(elements):
                    try:
                        university_data = self._parse_alternative_element(element, page_num, i + 1)
                        if university_data:
                            universities.append(university_data)
                    except:
                        continue
                
                if len(universities) >= 20:
                    break
        
        return universities

    def _parse_alternative_element(self, element, page_num, index):
        """解析备用元素"""
        try:
            text = element.get_text(separator='|', strip=True)
            
            # 查找大学名称
            name = self._extract_university_name_from_text(text)
            if not name:
                return None
            
            # 计算排名
            rank = (page_num - 1) * 30 + index
            
            # 推断国家
            country = self._infer_country_from_text(text)
            
            return {
                "name": name,
                "qs_ranking": rank,
                "country": country,
                "official_website": ""
            }
            
        except Exception as e:
            logger.debug(f"解析备用元素时出错: {e}")
            return None

    def _extract_university_name_from_text(self, text):
        """从文本中提取大学名称"""
        lines = [line.strip() for line in text.split('|') if line.strip()]
        
        for line in lines:
            # 跳过纯数字
            if re.match(r'^\d+$', line):
                continue
            
            # 查找包含大学关键词的文本
            if any(keyword in line for keyword in ['University', 'College', 'Institute', 'School', 'Academy']):
                if 5 <= len(line) <= 150:
                    return line.strip()
        
        # 如果没有找到关键词，返回第一个合适长度的文本
        for line in lines:
            if 10 <= len(line) <= 100:
                return line.strip()
        
        return None

    def _normalize_country_name(self, country_text):
        """标准化国家名称"""
        country_mapping = {
            'US': 'United States', 'USA': 'United States',
            'UK': 'United Kingdom', 'GB': 'United Kingdom',
            'CN': 'China', 'SG': 'Singapore', 'AU': 'Australia',
            'CA': 'Canada', 'DE': 'Germany', 'FR': 'France',
            'JP': 'Japan', 'CH': 'Switzerland', 'NL': 'Netherlands'
        }
        
        if country_text.upper() in country_mapping:
            return country_mapping[country_text.upper()]
        
        return country_text

    def _infer_country_from_text(self, text):
        """从文本中推断国家"""
        country_patterns = {
            'United States': ['MIT', 'Harvard', 'Stanford', 'Caltech', 'University of California', 'Yale', 'Princeton'],
            'United Kingdom': ['Oxford', 'Cambridge', 'Imperial College', 'University College London', 'King\'s College'],
            'Singapore': ['Singapore', 'NUS', 'NTU'],
            'Australia': ['Melbourne', 'Sydney', 'UNSW', 'ANU', 'Monash'],
            'Canada': ['Toronto', 'McGill', 'British Columbia'],
            'China': ['Peking', 'Tsinghua', 'Fudan'],
            'Hong Kong': ['Hong Kong'],
            'Germany': ['Technical University of Munich', 'Munich'],
            'Switzerland': ['ETH Zurich', 'Zurich'],
            'Japan': ['Tokyo', 'Kyoto'],
            'France': ['Sorbonne', 'École'],
            'Netherlands': ['Amsterdam', 'Delft']
        }
        
        for country, patterns in country_patterns.items():
            if any(pattern in text for pattern in patterns):
                return country
        
        return ""

    def _clean_and_validate_data(self, universities):
        """清理和验证数据"""
        seen_names = set()
        clean_universities = []
        
        for uni in universities:
            if not uni.get('name'):
                continue
            
            name = uni['name'].strip()
            
            # 去重
            if name in seen_names:
                continue
            seen_names.add(name)
            
            # 验证数据有效性
            if len(name) >= 5 and 1 <= uni['qs_ranking'] <= 1000:
                clean_universities.append(uni)
        
        # 按排名排序
        clean_universities.sort(key=lambda x: x['qs_ranking'])
        
        return clean_universities

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("开始爬取完整的QS Top100排名数据")
    logger.info("目标: 每页30所大学，总共100所")
    logger.info("=" * 60)
    
    scraper = QSCompleteScraper()
    universities = scraper.scrape_complete_top100()
    
    if universities and len(universities) >= 80:  # 至少80所才算成功
        logger.info(f"成功获取 {len(universities)} 所大学的数据")
        
        # 处理数据
        processor = DataProcessor()
        processed_universities = []
        
        for uni in universities:
            processed_uni = {
                "name": uni['name'],
                "qs_ranking": uni['qs_ranking'],
                "country": uni['country'],
                "continent": processor.get_continent_by_country(uni['country']),
                "official_website": uni.get('official_website', ''),
            }
            processed_universities.append(processed_uni)
        
        # 保存数据
        output_file = "qs_complete_top100_2025.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到 {output_file}")
        
        # 显示前20名和统计信息
        logger.info("\n前20名大学:")
        for uni in processed_universities[:20]:
            logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']})")
        
        # 验证每页数据量
        logger.info("\n每页数据量验证:")
        for page in range(1, 5):
            start_rank = (page - 1) * 30 + 1
            end_rank = page * 30
            page_unis = [u for u in processed_universities if start_rank <= u['qs_ranking'] <= end_rank]
            logger.info(f"  第{page}页 (排名{start_rank}-{end_rank}): {len(page_unis)} 所大学")
        
        print(f"\n✅ 成功爬取 {len(processed_universities)} 所大学的完整QS数据")
        print(f"📁 数据已保存到: {output_file}")
        print(f"🎯 目标达成度: {len(processed_universities)}/100")
        
    else:
        logger.error(f"爬取失败，只获取到 {len(universities) if universities else 0} 所大学")
        print("❌ 爬取失败，数据量不足")

if __name__ == "__main__":
    main()
