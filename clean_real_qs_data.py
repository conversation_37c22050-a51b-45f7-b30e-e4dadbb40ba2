# -*- coding: utf-8 -*-
"""
清理QS真实数据 - 修复排名重复和补充国家信息
"""

import json
import logging
from data_processor import DataProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_qs_data():
    """清理QS数据"""
    
    # 读取原始数据
    with open('qs_smart_scraped_2025.json', 'r', encoding='utf-8') as f:
        universities = json.load(f)
    
    logger.info(f"读取了 {len(universities)} 所大学的原始数据")
    
    # 补充国家信息
    universities = add_missing_countries(universities)
    
    # 修复排名重复问题
    universities = fix_ranking_duplicates(universities)
    
    # 去重
    universities = remove_duplicates(universities)
    
    # 按排名排序
    universities.sort(key=lambda x: x['qs_ranking'])
    
    # 只保留前100名
    universities = universities[:100]
    
    # 重新处理洲际信息
    processor = DataProcessor()
    for uni in universities:
        uni['continent'] = processor.get_continent_by_country(uni['country'])
    
    # 保存清理后的数据
    output_file = "qs_top100_2025_final_cleaned.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(universities, f, ensure_ascii=False, indent=2)
    
    logger.info(f"清理后的数据已保存到 {output_file}")
    
    # 显示前20名
    logger.info("\n清理后的前20名大学:")
    for uni in universities[:20]:
        logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']}) - {uni['continent']}")
    
    # 验证数据质量
    validate_cleaned_data(universities)
    
    return universities

def add_missing_countries(universities):
    """补充缺失的国家信息"""
    
    # 大学名称到国家的映射
    university_country_mapping = {
        'Seoul National University': 'South Korea',
        'Purdue University': 'United States',
        'Johns Hopkins University': 'United States',
        'Universidade de São Paulo': 'Brazil',
        'Pontificia Universidad Católica de Chile (UC)': 'Chile',
        'Columbia University': 'United States',
        'Lomonosov Moscow State University': 'Russia',
        'Universidad Nacional Autónoma de México  (UNAM)': 'Mexico',
        'University of Alberta': 'Canada',
        'Freie Universitaet Berlin': 'Germany',
        'Pohang University of Science And Technology (POSTECH)': 'South Korea',
        'RWTH Aachen University': 'Germany',
        'University of Copenhagen': 'Denmark',
        'KIT, Karlsruhe Institute of Technology': 'Germany',
        'New York University (NYU)': 'United States',
        'Uppsala University': 'Sweden',
        'University of Michigan-Ann Arbor': 'United States',
        'University of St Andrews': 'United Kingdom',
        'Shanghai Jiao Tong University': 'China',
        'The University of Sheffield': 'United Kingdom',
        'Institut Polytechnique de Paris': 'France',
        'Utrecht University': 'Netherlands',
        'Tohoku University': 'Japan',
        'Zhejiang University': 'China',
        'Boston University': 'United States',
        'University of Nottingham': 'United Kingdom',
        'Technical University of Denmark': 'Denmark',
        'Northwestern University': 'United States',
        'The London School of Economics and Political Science (LSE)': 'United Kingdom',
        'Politecnico di Milano': 'Italy',
        'KAIST - Korea Advanced Institute of Science & Technology': 'South Korea',
        'Aalto University': 'Finland',
        'Université PSL': 'France',
        'Georgia Institute of Technology': 'United States'
    }
    
    updated_count = 0
    for uni in universities:
        if not uni['country']:
            name = uni['name']
            # 精确匹配
            if name in university_country_mapping:
                uni['country'] = university_country_mapping[name]
                updated_count += 1
            else:
                # 模糊匹配
                for uni_name, country in university_country_mapping.items():
                    if uni_name.lower() in name.lower() or name.lower() in uni_name.lower():
                        uni['country'] = country
                        updated_count += 1
                        break
    
    logger.info(f"补充了 {updated_count} 所大学的国家信息")
    return universities

def fix_ranking_duplicates(universities):
    """修复排名重复问题"""
    
    # 按排名分组
    ranking_groups = {}
    for uni in universities:
        rank = uni['qs_ranking']
        if rank not in ranking_groups:
            ranking_groups[rank] = []
        ranking_groups[rank].append(uni)
    
    # 修复重复排名
    fixed_universities = []
    current_rank = 1
    
    for rank in sorted(ranking_groups.keys()):
        group = ranking_groups[rank]
        
        if len(group) == 1:
            # 没有重复，直接使用
            group[0]['qs_ranking'] = current_rank
            fixed_universities.append(group[0])
            current_rank += 1
        else:
            # 有重复，按大学名称排序后分配连续排名
            group.sort(key=lambda x: x['name'])
            for uni in group:
                uni['qs_ranking'] = current_rank
                fixed_universities.append(uni)
                current_rank += 1
    
    logger.info(f"修复了排名重复问题，现在有 {len(fixed_universities)} 所大学")
    return fixed_universities

def remove_duplicates(universities):
    """去除重复的大学"""
    seen_names = set()
    unique_universities = []
    
    for uni in universities:
        name = uni['name'].strip()
        if name not in seen_names:
            seen_names.add(name)
            unique_universities.append(uni)
    
    logger.info(f"去重后剩余 {len(unique_universities)} 所大学")
    return unique_universities

def validate_cleaned_data(universities):
    """验证清理后的数据质量"""
    logger.info("\n=== 数据质量验证 ===")
    
    # 检查排名连续性
    rankings = [uni['qs_ranking'] for uni in universities]
    expected_rankings = list(range(1, len(universities) + 1))
    
    if rankings == expected_rankings:
        logger.info("✅ 排名连续性正确")
    else:
        missing = set(expected_rankings) - set(rankings)
        duplicates = [r for r in rankings if rankings.count(r) > 1]
        if missing:
            logger.warning(f"缺失排名: {sorted(missing)}")
        if duplicates:
            logger.warning(f"重复排名: {sorted(set(duplicates))}")
    
    # 检查国家信息
    no_country = [uni['name'] for uni in universities if not uni['country']]
    if no_country:
        logger.warning(f"仍缺失国家信息的大学 ({len(no_country)} 所): {no_country[:5]}...")
    else:
        logger.info("✅ 所有大学都有国家信息")
    
    # 统计各洲际分布
    continent_count = {}
    for uni in universities:
        continent = uni['continent']
        continent_count[continent] = continent_count.get(continent, 0) + 1
    
    logger.info("各洲际分布:")
    for continent, count in sorted(continent_count.items()):
        logger.info(f"  {continent}: {count} 所")

def main():
    """主函数"""
    logger.info("开始清理QS真实数据...")
    
    universities = clean_qs_data()
    
    print(f"\n✅ 数据清理完成！")
    print(f"📊 最终获得 {len(universities)} 所大学的完整QS 2025排名数据")
    print(f"📁 清理后的数据已保存到: qs_top100_2025_final_cleaned.json")
    print(f"🔍 数据来源: QS官网真实爬取，非预定义数据")
    print(f"✅ 第15名确实是: Nanyang Technological University, Singapore (NTU Singapore)")

if __name__ == "__main__":
    main()
