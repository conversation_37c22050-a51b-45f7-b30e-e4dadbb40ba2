# -*- coding: utf-8 -*-
"""
QS改进版爬虫 - 专门处理QS网站的分页结构获取Top100真实数据
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSImprovedScraper:
    """QS改进版爬虫类"""
    
    def __init__(self):
        self.driver = None
        self.base_url = "https://www.qschina.cn/university-rankings/world-university-rankings/2025"
        self.universities = []
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 使用无头模式
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 使用当前目录的ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            
            if os.path.exists(chromedriver_path):
                logger.info(f"使用本地ChromeDriver: {chromedriver_path}")
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                logger.error(f"ChromeDriver不存在: {chromedriver_path}")
                return False
            
            # 隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("Chrome浏览器驱动设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False
    
    def scrape_top100_rankings(self):
        """爬取Top100排名数据"""
        if not self.setup_driver():
            return []
        
        try:
            logger.info("正在访问QS排名页面...")
            self.driver.get(self.base_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            logger.info("页面加载完成，开始处理分页数据...")
            time.sleep(5)
            
            # 尝试加载更多数据
            self._load_more_data()
            
            # 提取排名数据
            universities = self._extract_comprehensive_data()
            
            return universities[:100]  # 只返回前100名
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()
    
    def _load_more_data(self):
        """加载更多数据"""
        try:
            # 方法1: 滚动页面
            logger.info("滚动页面加载数据...")
            for i in range(20):  # 滚动20次
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(1)
                
                # 检查是否有新内容加载
                current_height = self.driver.execute_script("return document.body.scrollHeight")
                if i > 0 and current_height == getattr(self, '_last_height', 0):
                    break
                self._last_height = current_height
            
            # 方法2: 查找并点击分页按钮
            logger.info("查找分页按钮...")
            pagination_selectors = [
                "a[href*='page']",
                ".pagination a",
                ".pager a",
                "button[data-page]",
                ".next-page",
                ".load-more",
                "a:contains('下一页')",
                "a:contains('Next')",
                "a:contains('更多')",
            ]
            
            for selector in pagination_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements[:5]:  # 最多点击5个分页链接
                        if element.is_displayed() and element.is_enabled():
                            self.driver.execute_script("arguments[0].click();", element)
                            logger.info(f"点击了分页按钮: {selector}")
                            time.sleep(3)
                            break
                except:
                    continue
            
            # 方法3: 尝试修改URL参数获取更多数据
            logger.info("尝试通过URL参数获取更多数据...")
            for page in range(2, 6):  # 尝试获取第2-5页
                try:
                    page_url = f"{self.base_url}?page={page}"
                    self.driver.get(page_url)
                    time.sleep(3)
                    
                    # 检查是否有新的大学数据
                    page_source = self.driver.page_source
                    if 'University' in page_source or 'College' in page_source:
                        logger.info(f"成功加载第{page}页数据")
                    else:
                        break
                except:
                    break
            
            # 回到主页面
            self.driver.get(self.base_url)
            time.sleep(3)
            
        except Exception as e:
            logger.error(f"加载更多数据时出错: {e}")
    
    def _extract_comprehensive_data(self):
        """综合提取数据"""
        universities = []
        
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 方法1: 从表格中提取
            universities.extend(self._extract_from_tables(soup))
            
            # 方法2: 从列表中提取
            universities.extend(self._extract_from_lists(soup))
            
            # 方法3: 从JavaScript数据中提取
            universities.extend(self._extract_from_javascript(page_source))
            
            # 方法4: 从特定的QS结构中提取
            universities.extend(self._extract_from_qs_structure(soup))
            
            # 去重和排序
            universities = self._deduplicate_and_sort(universities)
            
            logger.info(f"综合提取了 {len(universities)} 所大学的数据")
            return universities
            
        except Exception as e:
            logger.error(f"综合提取数据时出错: {e}")
            return []
    
    def _extract_from_tables(self, soup):
        """从表格中提取数据"""
        universities = []
        
        try:
            # 查找所有表格
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                for i, row in enumerate(rows[1:]):  # 跳过表头
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        try:
                            # 提取排名和大学名称
                            rank_text = cells[0].get_text(strip=True)
                            name_text = cells[1].get_text(strip=True)
                            
                            rank_match = re.search(r'(\d+)', rank_text)
                            if rank_match and name_text:
                                rank = int(rank_match.group(1))
                                
                                # 提取国家（通常在第3列）
                                country = ""
                                if len(cells) > 2:
                                    country = cells[2].get_text(strip=True)
                                
                                if rank <= 100 and len(name_text) > 3:
                                    universities.append({
                                        "name": name_text,
                                        "qs_ranking": rank,
                                        "country": country,
                                        "score": "",
                                        "official_website": "",
                                        "college_website": ""
                                    })
                        except:
                            continue
            
            logger.info(f"从表格中提取了 {len(universities)} 所大学")
            return universities
            
        except Exception as e:
            logger.error(f"从表格提取数据时出错: {e}")
            return []
    
    def _extract_from_lists(self, soup):
        """从列表中提取数据"""
        universities = []
        
        try:
            # 查找可能的列表结构
            list_selectors = [
                '.ranking-item',
                '.university-item',
                '.ranking-row',
                '.university-row',
                '[data-rank]',
                '[data-university]',
                '.ranking-list > div',
                '.university-list > div',
            ]
            
            for selector in list_selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                    
                    for i, element in enumerate(elements[:100]):
                        try:
                            text = element.get_text(strip=True)
                            lines = [line.strip() for line in text.split('\n') if line.strip()]
                            
                            # 查找排名
                            rank = i + 1
                            for line in lines:
                                rank_match = re.search(r'^(\d+)', line)
                                if rank_match:
                                    rank = int(rank_match.group(1))
                                    break
                            
                            # 查找大学名称
                            name = ""
                            for line in lines:
                                if any(keyword in line for keyword in ['University', 'College', 'Institute']):
                                    if len(line) > 5 and len(line) < 100:
                                        name = line
                                        break
                            
                            if name and rank <= 100:
                                universities.append({
                                    "name": name,
                                    "qs_ranking": rank,
                                    "country": "",
                                    "score": "",
                                    "official_website": "",
                                    "college_website": ""
                                })
                        except:
                            continue
                    
                    if universities:
                        break
            
            logger.info(f"从列表中提取了 {len(universities)} 所大学")
            return universities
            
        except Exception as e:
            logger.error(f"从列表提取数据时出错: {e}")
            return []
    
    def _extract_from_javascript(self, page_source):
        """从JavaScript数据中提取"""
        universities = []
        
        try:
            # 查找JavaScript中的数据
            js_patterns = [
                r'var\s+rankingData\s*=\s*(\[.*?\]);',
                r'window\.rankingData\s*=\s*(\[.*?\]);',
                r'"universities":\s*(\[.*?\])',
                r'"rankings":\s*(\[.*?\])',
                r'rankings:\s*(\[.*?\])',
                r'universities:\s*(\[.*?\])',
            ]
            
            for pattern in js_patterns:
                matches = re.findall(pattern, page_source, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        if isinstance(data, list) and len(data) > 5:
                            logger.info(f"从JavaScript中找到 {len(data)} 条数据")
                            
                            for item in data:
                                if isinstance(item, dict):
                                    name = item.get('name') or item.get('university_name')
                                    rank = item.get('rank') or item.get('ranking')
                                    country = item.get('country') or item.get('location')
                                    
                                    if name and rank:
                                        universities.append({
                                            "name": str(name).strip(),
                                            "qs_ranking": int(rank),
                                            "country": str(country).strip() if country else "",
                                            "score": "",
                                            "official_website": "",
                                            "college_website": ""
                                        })
                            
                            if universities:
                                break
                    except json.JSONDecodeError:
                        continue
                
                if universities:
                    break
            
            logger.info(f"从JavaScript中提取了 {len(universities)} 所大学")
            return universities
            
        except Exception as e:
            logger.error(f"从JavaScript提取数据时出错: {e}")
            return []
    
    def _extract_from_qs_structure(self, soup):
        """从QS特定结构中提取数据"""
        universities = []
        
        try:
            # 查找QS特定的结构
            qs_selectors = [
                '.ranking-data',
                '.university-ranking',
                '.qs-ranking',
                '[data-qs-rank]',
                '.ranking-table-row',
                '.university-profile',
            ]
            
            for selector in qs_selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"使用QS选择器 '{selector}' 找到 {len(elements)} 个元素")
                    # 这里可以添加特定的QS结构解析逻辑
                    break
            
            return universities
            
        except Exception as e:
            logger.error(f"从QS结构提取数据时出错: {e}")
            return []
    
    def _deduplicate_and_sort(self, universities):
        """去重和排序"""
        try:
            # 去重
            seen = set()
            unique_universities = []
            
            for uni in universities:
                key = f"{uni['name']}_{uni['qs_ranking']}"
                if key not in seen:
                    seen.add(key)
                    unique_universities.append(uni)
            
            # 排序
            unique_universities.sort(key=lambda x: x['qs_ranking'])
            
            logger.info(f"去重后剩余 {len(unique_universities)} 所大学")
            return unique_universities
            
        except Exception as e:
            logger.error(f"去重和排序时出错: {e}")
            return universities

def main():
    """主函数"""
    logger.info("开始爬取QS真实Top100排名数据（改进版）...")
    
    scraper = QSImprovedScraper()
    universities = scraper.scrape_top100_rankings()
    
    if universities:
        logger.info(f"成功获取 {len(universities)} 所大学的真实数据")
        
        # 处理数据
        processor = DataProcessor()
        logger.info("开始处理大学数据...")
        processed_universities = processor.batch_process_universities(universities, delay=0.1)
        
        # 保存数据
        output_file = "qs_real_top100_improved.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到 {output_file}")
        
        # 显示前10名
        logger.info("\n前10名大学:")
        for i, uni in enumerate(processed_universities[:10]):
            logger.info(f"  {i+1}. {uni['name']} ({uni['country']}) - 排名: {uni['qs_ranking']}")
        
        print(f"\n✅ 成功爬取 {len(processed_universities)} 所大学的真实QS 2025排名数据")
        print(f"📁 数据已保存到: {output_file}")
        
    else:
        logger.error("未能获取到任何真实的排名数据")
        print("❌ 爬取失败，请检查网络连接或网站结构是否发生变化")

if __name__ == "__main__":
    main()
