# -*- coding: utf-8 -*-
"""
清理QS数据 - 修正从QS官网爬取的数据格式
"""

import json
import re
from data_processor import DataProcessor

def clean_qs_data():
    """清理QS数据"""
    
    # 读取原始数据
    with open('qs_real_top100_improved.json', 'r', encoding='utf-8') as f:
        universities = json.load(f)
    
    cleaned_universities = []
    processor = DataProcessor()
    
    for uni in universities:
        # 清理大学名称和国家
        name = uni['name']
        country = ""
        score = uni['country']  # 分数被错误地放在了country字段
        
        # 从名称中提取国家
        if 'United States' in name:
            country = 'United States'
            name = name.replace('United States', '').strip()
        elif 'United Kingdom' in name:
            country = 'United Kingdom'
            name = name.replace('United Kingdom', '').strip()
        elif 'China (Mainland)' in name:
            country = 'China'
            name = name.replace('China (Mainland)', '').strip()
        elif 'Hong Kong SAR' in name:
            country = 'Hong Kong'
            name = name.replace('Hong Kong SAR', '').strip()
        elif 'Singapore' in name:
            country = 'Singapore'
            name = name.replace('Singapore', '').strip()
        elif 'Australia' in name:
            country = 'Australia'
            name = name.replace('Australia', '').strip()
        elif 'Switzerland' in name:
            country = 'Switzerland'
            name = name.replace('Switzerland', '').strip()
        elif 'France' in name:
            country = 'France'
            name = name.replace('France', '').strip()
        elif 'Canada' in name:
            country = 'Canada'
            name = name.replace('Canada', '').strip()
        
        # 获取洲际
        continent = processor.get_continent_by_country(country)
        
        # 获取缩写
        abbreviation = processor.get_university_abbreviation(name)
        
        # 创建清理后的数据
        cleaned_uni = {
            "name": name,
            "abbreviation": abbreviation,
            "qs_ranking": uni['qs_ranking'],
            "country": country,
            "continent": continent,
            "score": score,
            "official_website": "",
            "college_website": ""
        }
        
        cleaned_universities.append(cleaned_uni)
    
    # 保存清理后的数据
    with open('qs_real_top25_cleaned.json', 'w', encoding='utf-8') as f:
        json.dump(cleaned_universities, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 成功清理 {len(cleaned_universities)} 所大学的数据")
    print("📁 清理后的数据已保存到: qs_real_top25_cleaned.json")
    
    # 显示前10名
    print("\n🏆 QS 2025世界大学排名前10名:")
    for i, uni in enumerate(cleaned_universities[:10]):
        print(f"  {i+1:2d}. {uni['name']} ({uni['country']}) - 排名: {uni['qs_ranking']} - 分数: {uni['score']}")
    
    return cleaned_universities

if __name__ == "__main__":
    clean_qs_data()
