# -*- coding: utf-8 -*-
"""
QS精确爬虫 - 修复UCL缺失问题
基于调试结果，我们知道页面有30个.uni-link元素，问题在于排名分配
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSPreciseScraper:
    """QS精确爬虫 - 确保获取完整的30所大学包括UCL"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.topuniversities.com/world-university-rankings"

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            if os.path.exists(chromedriver_path):
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome驱动设置成功")
                return True
            else:
                logger.error("ChromeDriver不存在")
                return False

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_precise_top100(self):
        """精确爬取Top100数据"""
        if not self.setup_driver():
            return []

        all_universities = []

        try:
            # 爬取4页数据
            for page_num in range(1, 5):
                logger.info(f"正在爬取第 {page_num} 页...")

                if page_num == 1:
                    page_url = self.base_url
                else:
                    page_url = f"{self.base_url}?page={page_num}"

                self.driver.get(page_url)

                WebDriverWait(self.driver, 30).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )

                time.sleep(8)

                if page_num == 1:
                    self._handle_popups()

                # 确保数据加载
                self._ensure_data_loaded()

                # 精确提取当前页面数据
                page_universities = self._extract_precise_page_data(page_num)

                if page_universities:
                    logger.info(f"第 {page_num} 页获取到 {len(page_universities)} 所大学")

                    # 显示获取到的大学（用于验证）
                    for uni in page_universities:
                        logger.info(f"  {uni['qs_ranking']}. {uni['name']}")

                    all_universities.extend(page_universities)
                else:
                    logger.warning(f"第 {page_num} 页没有获取到数据")

                if len(all_universities) >= 100:
                    break

                time.sleep(3)

            # 清理数据
            clean_universities = self._clean_data(all_universities)

            # 验证UCL是否存在
            ucl_found = any('UCL' in uni['name'] or 'University College London' in uni['name'] for uni in clean_universities)
            logger.info(f"UCL是否找到: {'✅' if ucl_found else '❌'}")

            return clean_universities[:100]

        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _handle_popups(self):
        """处理弹窗"""
        try:
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                ".cookie-accept",
                "#accept-cookies"
            ]

            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info("处理了Cookie弹窗")
                            time.sleep(2)
                            return
                except:
                    continue

        except Exception as e:
            logger.debug(f"处理弹窗时出错: {e}")

    def _ensure_data_loaded(self):
        """确保数据完全加载"""
        try:
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".uni-link"))
            )

            # 滚动页面确保所有数据加载
            for i in range(5):
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(2)

            time.sleep(5)

        except Exception as e:
            logger.warning(f"确保数据加载时出错: {e}")

    def _extract_precise_page_data(self, page_num):
        """精确提取当前页面数据"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # 获取所有.uni-link元素
            uni_links = soup.select('.uni-link')
            logger.info(f"第 {page_num} 页找到 {len(uni_links)} 个 .uni-link 元素")

            universities = []

            for i, link in enumerate(uni_links):
                try:
                    name = link.get_text(strip=True)
                    if not name or len(name) < 2:  # 修复：允许UCL这样的短名称
                        continue

                    # 关键修复：不使用简单的索引计算排名
                    # 而是尝试从页面上下文中提取真实排名
                    rank = self._extract_real_ranking(link, page_num, i)

                    # 提取国家信息
                    country = self._extract_country_from_context(link)

                    university_data = {
                        "name": name,
                        "qs_ranking": rank,
                        "country": country,
                        "official_website": ""
                    }

                    universities.append(university_data)

                except Exception as e:
                    logger.debug(f"解析第 {i+1} 个链接时出错: {e}")
                    continue

            return universities

        except Exception as e:
            logger.error(f"提取第 {page_num} 页数据时出错: {e}")
            return []

    def _extract_real_ranking(self, link_element, page_num, index):
        """提取真实排名"""
        try:
            # 方法1: 查找父元素或兄弟元素中的排名数字
            parent = link_element.parent
            if parent:
                # 在父元素的所有文本中查找排名
                parent_text = parent.get_text()

                # 查找1-1000之间的数字
                rank_matches = re.findall(r'\b(\d+)\b', parent_text)
                for match in rank_matches:
                    rank = int(match)
                    if 1 <= rank <= 1000:
                        # 验证这个排名是否合理（应该在当前页面的范围内）
                        expected_min = (page_num - 1) * 30 + 1
                        expected_max = page_num * 30
                        if expected_min <= rank <= expected_max:
                            return rank

            # 方法2: 查找更大范围的上下文
            grandparent = parent.parent if parent and parent.parent else None
            if grandparent:
                grandparent_text = grandparent.get_text()
                rank_matches = re.findall(r'\b(\d+)\b', grandparent_text)
                for match in rank_matches:
                    rank = int(match)
                    if 1 <= rank <= 1000:
                        expected_min = (page_num - 1) * 30 + 1
                        expected_max = page_num * 30
                        if expected_min <= rank <= expected_max:
                            return rank

            # 方法3: 如果找不到明确的排名，使用基于页面和索引的计算
            # 但要考虑可能的偏移
            calculated_rank = (page_num - 1) * 30 + index + 1

            # 特殊处理：如果是第一页且索引>=8，可能需要调整
            if page_num == 1 and index >= 8:
                # 检查是否是UCL
                name = link_element.get_text(strip=True)
                if 'UCL' in name or 'University College London' in name:
                    return 9  # UCL应该是第9名

            return calculated_rank

        except Exception as e:
            logger.debug(f"提取排名时出错: {e}")
            # 默认使用计算的排名
            return (page_num - 1) * 30 + index + 1

    def _extract_country_from_context(self, element):
        """从元素上下文中提取国家信息"""
        try:
            parent = element.parent
            if parent:
                # 查找包含国家信息的元素
                country_elements = parent.find_all(class_=re.compile(r'country|location|flag'))
                for country_elem in country_elements:
                    country_text = country_elem.get_text(strip=True)
                    if country_text and len(country_text) < 50:
                        return self._clean_country_name(country_text)

                # 从父元素文本中推断
                parent_text = parent.get_text()
                return self._infer_country_from_text(parent_text)

            return ""

        except Exception as e:
            logger.debug(f"提取国家信息时出错: {e}")
            return ""

    def _clean_country_name(self, country_text):
        """清理国家名称"""
        if ',' in country_text:
            parts = country_text.split(',')
            return parts[-1].strip()
        return country_text.strip()

    def _infer_country_from_text(self, text):
        """从文本中推断国家"""
        country_patterns = {
            'United States': ['MIT', 'Harvard', 'Stanford', 'Caltech', 'University of California', 'Yale', 'Princeton'],
            'United Kingdom': ['Oxford', 'Cambridge', 'Imperial College', 'University College London', 'UCL', 'King\'s College'],
            'Singapore': ['Singapore', 'NUS', 'NTU'],
            'Australia': ['Melbourne', 'Sydney', 'UNSW', 'ANU', 'Monash'],
            'Canada': ['Toronto', 'McGill', 'British Columbia'],
            'China': ['Peking', 'Tsinghua', 'Fudan'],
            'Hong Kong': ['Hong Kong'],
            'Germany': ['Technical University of Munich', 'Munich'],
            'Switzerland': ['ETH Zurich', 'Zurich'],
            'Japan': ['Tokyo', 'Kyoto'],
            'France': ['Sorbonne', 'École', 'PSL'],
            'Netherlands': ['Amsterdam', 'Delft']
        }

        for country, patterns in country_patterns.items():
            if any(pattern in text for pattern in patterns):
                return country

        return ""

    def _clean_data(self, universities):
        """清理数据"""
        seen_names = set()
        clean_universities = []

        for uni in universities:
            if not uni.get('name'):
                continue

            name = uni['name'].strip()

            # 去重
            if name in seen_names:
                continue
            seen_names.add(name)

            # 验证数据有效性（修复：允许UCL这样的短名称）
            if len(name) >= 2 and 1 <= uni['qs_ranking'] <= 1000:
                clean_universities.append(uni)

        # 按排名排序
        clean_universities.sort(key=lambda x: x['qs_ranking'])

        return clean_universities

def main():
    """主函数"""
    logger.info("开始精确爬取QS Top100排名数据（修复UCL缺失）...")

    scraper = QSPreciseScraper()
    universities = scraper.scrape_precise_top100()

    if universities and len(universities) >= 80:
        logger.info(f"成功获取 {len(universities)} 所大学的数据")

        # 处理数据
        processor = DataProcessor()
        processed_universities = []

        for uni in universities:
            processed_uni = {
                "name": uni['name'],
                "qs_ranking": uni['qs_ranking'],
                "country": uni['country'],
                "continent": processor.get_continent_by_country(uni['country']),
                "official_website": uni.get('official_website', ''),
            }
            processed_universities.append(processed_uni)

        # 保存数据
        output_file = "qs_top100_2025_precise.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)

        logger.info(f"数据已保存到 {output_file}")

        # 验证前20名，特别关注第9名
        logger.info("\n前20名大学（重点验证第9名UCL）:")
        for uni in processed_universities[:20]:
            marker = " ⭐" if uni['qs_ranking'] == 9 else ""
            logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']}){marker}")

        # 特别检查第9名
        ninth_uni = next((uni for uni in processed_universities if uni['qs_ranking'] == 9), None)
        if ninth_uni:
            if 'UCL' in ninth_uni['name'] or 'University College London' in ninth_uni['name']:
                logger.info(f"\n✅ 修复成功！第9名是: {ninth_uni['name']}")
            else:
                logger.warning(f"\n⚠️ 第9名不是UCL，而是: {ninth_uni['name']}")
        else:
            logger.error("\n❌ 第9名仍然缺失")

        print(f"\n✅ 精确爬取完成！获取了 {len(processed_universities)} 所大学的数据")
        print(f"📁 数据已保存到: {output_file}")

    else:
        logger.error(f"精确爬取失败，只获取到 {len(universities) if universities else 0} 所大学")
        print("❌ 精确爬取失败")

if __name__ == "__main__":
    main()
