# -*- coding: utf-8 -*-
"""
调试第一页爬取问题 - 分析为什么遗漏了第9名UCL
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSFirstPageDebugger:
    """QS第一页调试器"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.topuniversities.com/world-university-rankings"

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 不使用无头模式，方便观察
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 使用本地ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            if os.path.exists(chromedriver_path):
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome驱动设置成功")
                return True
            else:
                logger.error("ChromeDriver不存在")
                return False

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def debug_first_page(self):
        """调试第一页数据获取"""
        if not self.setup_driver():
            return

        try:
            logger.info("开始调试第一页数据获取...")
            
            # 访问第一页
            logger.info(f"访问页面: {self.base_url}")
            self.driver.get(self.base_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            logger.info("页面加载完成")
            time.sleep(8)
            
            # 处理弹窗
            self._handle_popups()
            
            # 确保数据完全加载
            self._ensure_data_loaded()
            
            # 获取页面源码
            page_source = self.driver.page_source
            
            # 保存完整页面源码
            with open('debug_first_page_full.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            logger.info("完整页面源码已保存到 debug_first_page_full.html")
            
            # 解析页面
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 分析所有可能的选择器
            self._analyze_all_selectors(soup)
            
            # 专门查找UCL
            self._search_for_ucl(soup)
            
            # 分析排名结构
            self._analyze_ranking_structure(soup)
            
        except Exception as e:
            logger.error(f"调试过程中出错: {e}")
        finally:
            if self.driver:
                # 不要立即关闭，让用户可以观察页面
                input("按Enter键关闭浏览器...")
                self.driver.quit()

    def _handle_popups(self):
        """处理弹窗"""
        try:
            logger.info("处理弹窗...")
            
            # Cookie弹窗
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                ".cookie-accept",
                "#accept-cookies",
                ".accept-all",
                "[data-accept]"
            ]
            
            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info(f"点击了弹窗按钮: {selector}")
                            time.sleep(2)
                            return
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"处理弹窗时出错: {e}")

    def _ensure_data_loaded(self):
        """确保数据完全加载"""
        try:
            # 等待大学链接出现
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".uni-link"))
            )
            
            # 滚动页面
            logger.info("滚动页面确保数据加载...")
            for i in range(5):
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(2)
            
            time.sleep(5)
            
        except Exception as e:
            logger.warning(f"确保数据加载时出错: {e}")

    def _analyze_all_selectors(self, soup):
        """分析所有可能的选择器"""
        logger.info("\n=== 分析所有选择器 ===")
        
        selectors_to_test = [
            '.uni-link',
            'table tbody tr',
            '.ranking-item',
            '.university-item',
            '[data-rank]',
            '.ranking-data tr',
            '.rankings-table tr',
            'a[href*="universities"]'
        ]
        
        for selector in selectors_to_test:
            elements = soup.select(selector)
            logger.info(f"选择器 '{selector}': 找到 {len(elements)} 个元素")
            
            if elements:
                # 显示前5个元素的文本
                for i, elem in enumerate(elements[:5]):
                    text = elem.get_text(strip=True)[:100]  # 只显示前100个字符
                    logger.info(f"  {i+1}. {text}")

    def _search_for_ucl(self, soup):
        """专门搜索UCL"""
        logger.info("\n=== 搜索UCL ===")
        
        # 在整个页面中搜索UCL相关文本
        page_text = soup.get_text()
        
        ucl_keywords = [
            'University College London',
            'UCL',
            'London UCL'
        ]
        
        for keyword in ucl_keywords:
            if keyword in page_text:
                logger.info(f"✅ 在页面中找到关键词: {keyword}")
                
                # 查找包含该关键词的元素
                elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
                for elem in elements:
                    parent = elem.parent if elem.parent else elem
                    logger.info(f"  包含'{keyword}'的元素: {parent.name} - {elem.strip()}")
            else:
                logger.warning(f"❌ 未在页面中找到关键词: {keyword}")

    def _analyze_ranking_structure(self, soup):
        """分析排名结构"""
        logger.info("\n=== 分析排名结构 ===")
        
        # 查找所有包含数字1-30的元素
        for rank in range(1, 31):
            rank_pattern = rf'\b{rank}\b'
            elements = soup.find_all(text=re.compile(rank_pattern))
            
            if elements:
                logger.info(f"排名 {rank}:")
                for elem in elements[:3]:  # 只显示前3个匹配
                    parent = elem.parent if elem.parent else elem
                    context = parent.get_text(strip=True)[:200]
                    logger.info(f"  {context}")
            else:
                logger.warning(f"❌ 未找到排名 {rank}")

    def extract_universities_detailed(self, soup):
        """详细提取大学信息"""
        logger.info("\n=== 详细提取大学信息 ===")
        
        universities = []
        
        # 使用.uni-link选择器
        uni_links = soup.select('.uni-link')
        logger.info(f"找到 {len(uni_links)} 个 .uni-link 元素")
        
        for i, link in enumerate(uni_links):
            try:
                name = link.get_text(strip=True)
                rank = i + 1
                
                # 查找父元素中的排名信息
                parent = link.parent
                if parent:
                    parent_text = parent.get_text()
                    rank_match = re.search(r'\b(\d+)\b', parent_text)
                    if rank_match:
                        found_rank = int(rank_match.group(1))
                        if 1 <= found_rank <= 30:
                            rank = found_rank
                
                university_data = {
                    "name": name,
                    "qs_ranking": rank,
                    "country": "",
                    "official_website": ""
                }
                
                universities.append(university_data)
                logger.info(f"  {rank}. {name}")
                
            except Exception as e:
                logger.debug(f"解析第 {i+1} 个链接时出错: {e}")
                continue
        
        # 按排名排序
        universities.sort(key=lambda x: x['qs_ranking'])
        
        # 检查是否有UCL
        ucl_found = any('UCL' in uni['name'] or 'University College London' in uni['name'] for uni in universities)
        logger.info(f"\n{'✅' if ucl_found else '❌'} UCL在提取的数据中: {ucl_found}")
        
        # 检查排名连续性
        rankings = [uni['qs_ranking'] for uni in universities]
        missing_ranks = []
        for i in range(1, 31):
            if i not in rankings:
                missing_ranks.append(i)
        
        if missing_ranks:
            logger.warning(f"缺失的排名: {missing_ranks}")
        else:
            logger.info("✅ 排名1-30完整")
        
        return universities

def main():
    """主函数"""
    logger.info("开始调试第一页UCL缺失问题...")
    
    debugger = QSFirstPageDebugger()
    debugger.debug_first_page()
    
    print("\n调试完成！请查看生成的HTML文件和日志输出。")

if __name__ == "__main__":
    main()
