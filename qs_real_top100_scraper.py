# -*- coding: utf-8 -*-
"""
QS真实Top100爬虫 - 专门爬取QS官网真实的2025年排名数据
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSRealTop100Scraper:
    """QS真实Top100爬虫类"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.qschina.cn/university-rankings/world-university-rankings/2025"
        self.universities = []

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 不使用无头模式，这样可以看到浏览器操作
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 使用当前目录的ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')

            # 检查ChromeDriver是否存在
            if os.path.exists(chromedriver_path):
                logger.info(f"使用本地ChromeDriver: {chromedriver_path}")
                # 确保ChromeDriver有执行权限
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                logger.error(f"ChromeDriver不存在: {chromedriver_path}")
                return False

            # 隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("Chrome浏览器驱动设置成功")
            return True

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_real_rankings(self):
        """爬取真实的QS排名数据"""
        if not self.setup_driver():
            return []

        try:
            logger.info("正在访问QS排名页面...")
            self.driver.get(self.base_url)

            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            logger.info("页面加载完成，等待排名数据...")
            time.sleep(5)

            # 尝试多种方法来获取排名数据
            universities = self._extract_ranking_data()

            if not universities:
                # 如果第一次失败，尝试滚动页面加载更多数据
                logger.info("第一次提取失败，尝试滚动页面...")
                self._scroll_and_load_data()
                universities = self._extract_ranking_data()

            if not universities:
                # 如果还是失败，尝试点击加载更多按钮
                logger.info("尝试点击加载更多按钮...")
                self._click_load_more_buttons()
                universities = self._extract_ranking_data()

            return universities[:100]  # 只返回前100名

        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _extract_ranking_data(self):
        """提取排名数据"""
        universities = []

        try:
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # 尝试多种选择器来找到排名数据
            selectors = [
                # 表格相关选择器
                'table.ranking-table tbody tr',
                'table tbody tr',
                '.ranking-table tr',
                'table tr',

                # 列表相关选择器
                '.ranking-item',
                '.university-item',
                '.ranking-row',
                '.university-row',
                '.rank-item',

                # 通用选择器
                '[data-rank]',
                '[data-university]',
                '[data-ranking]',
                '.university-ranking-item',
                '.ranking-list-item',

                # 可能的容器选择器
                '.ranking-container > div',
                '.university-list > div',
                '.rankings-content > div',
                '.ranking-results > div',
            ]

            for selector in selectors:
                elements = soup.select(selector)
                if elements and len(elements) >= 10:  # 确保找到足够的数据
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                    universities = self._parse_elements(elements)
                    if len(universities) >= 10:  # 确保解析出足够的大学
                        break

            # 如果上述方法都失败，尝试从JavaScript变量中提取
            if not universities:
                universities = self._extract_from_javascript(page_source)

            # 如果还是失败，尝试从文本中提取
            if not universities:
                universities = self._extract_from_text(soup)

            return universities

        except Exception as e:
            logger.error(f"提取排名数据时出错: {e}")
            return []

    def _parse_elements(self, elements):
        """解析HTML元素"""
        universities = []
        seen_universities = set()  # 用于去重

        for i, element in enumerate(elements):
            try:
                # 获取元素的所有文本
                text = element.get_text(separator='\n', strip=True)
                lines = [line.strip() for line in text.split('\n') if line.strip()]

                if not lines:
                    continue

                # 提取排名
                rank = self._extract_rank_from_text(lines, i + 1)

                # 提取大学名称
                name = self._extract_university_name_from_text(lines)

                # 跳过无效的大学名称
                if not name or name in ['China (Mainland)', 'Hong Kong SAR', 'United States', 'United Kingdom']:
                    continue

                # 提取国家
                country = self._extract_country_from_text(lines)

                # 提取分数（如果有）
                score = self._extract_score_from_text(lines)

                # 创建唯一标识符用于去重
                university_key = f"{name}_{rank}"

                if name and rank <= 100 and university_key not in seen_universities:
                    university = {
                        "name": name,
                        "qs_ranking": rank,
                        "country": country,
                        "score": score,
                        "official_website": "",
                        "college_website": ""
                    }
                    universities.append(university)
                    seen_universities.add(university_key)

                    if len(universities) % 10 == 0:
                        logger.info(f"已解析 {len(universities)} 所大学")

            except Exception as e:
                logger.debug(f"解析第 {i+1} 个元素时出错: {e}")
                continue

        # 按排名排序
        universities.sort(key=lambda x: x['qs_ranking'])

        logger.info(f"成功解析 {len(universities)} 所大学的数据")
        return universities

    def _extract_rank_from_text(self, lines, default_rank):
        """从文本中提取排名"""
        for line in lines:
            # 查找纯数字或带有排名标识的数字
            rank_patterns = [
                r'^(\d+)$',  # 纯数字
                r'^(\d+)\s*$',  # 数字加空格
                r'^\s*(\d+)\s*$',  # 前后有空格的数字
                r'rank\s*:?\s*(\d+)',  # rank: 123
                r'排名\s*:?\s*(\d+)',  # 排名: 123
                r'#(\d+)',  # #123
            ]

            for pattern in rank_patterns:
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    rank = int(match.group(1))
                    if 1 <= rank <= 1000:  # 合理的排名范围
                        return rank

        return default_rank

    def _extract_university_name_from_text(self, lines):
        """从文本中提取大学名称"""
        for line in lines:
            # 跳过纯数字行
            if re.match(r'^\d+$', line.strip()):
                continue

            # 跳过分数行
            if re.match(r'^\d+\.?\d*$', line.strip()):
                continue

            # 查找包含大学关键词的行
            university_keywords = ['University', 'College', 'Institute', 'School', 'Academy', 'Polytechnic']
            if any(keyword in line for keyword in university_keywords):
                if len(line) > 5 and len(line) < 150:  # 合理的长度
                    return line.strip()

            # 如果没有关键词，但是长度合适且不是国家名，也可能是大学名
            if len(line) > 10 and len(line) < 100:
                # 排除常见的国家名
                common_countries = ['United States', 'United Kingdom', 'China', 'Germany', 'Australia', 'Canada', 'Japan', 'France', 'Singapore', 'Switzerland']
                if line not in common_countries:
                    return line.strip()

        return ""

    def _extract_country_from_text(self, lines):
        """从文本中提取国家"""
        common_countries = [
            'United States', 'United Kingdom', 'China', 'Germany', 'Australia',
            'Canada', 'Japan', 'France', 'Singapore', 'Switzerland', 'Netherlands',
            'Sweden', 'Denmark', 'Norway', 'South Korea', 'Hong Kong', 'Taiwan',
            'India', 'Malaysia', 'Thailand', 'Indonesia', 'Philippines', 'Vietnam'
        ]

        for line in lines:
            for country in common_countries:
                if country.lower() in line.lower():
                    return country

        # 如果没有找到完整匹配，查找可能的国家名
        for line in lines:
            if len(line) < 30 and re.match(r'^[A-Za-z\s]+$', line):
                return line.strip()

        return ""

    def _extract_score_from_text(self, lines):
        """从文本中提取分数"""
        for line in lines:
            # 查找小数分数
            score_match = re.search(r'(\d+\.\d+)', line)
            if score_match:
                score = float(score_match.group(1))
                if 0 <= score <= 100:  # 合理的分数范围
                    return score

        return ""

    def _scroll_and_load_data(self):
        """滚动页面加载更多数据"""
        try:
            last_height = self.driver.execute_script("return document.body.scrollHeight")

            for _ in range(10):  # 最多滚动10次
                # 滚动到页面底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

                # 检查页面高度是否改变
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height

            logger.info("完成页面滚动")

        except Exception as e:
            logger.error(f"滚动页面时出错: {e}")

    def _click_load_more_buttons(self):
        """点击加载更多按钮"""
        try:
            load_more_selectors = [
                "button:contains('加载更多')",
                "button:contains('显示更多')",
                "button:contains('Load More')",
                "button:contains('Show More')",
                ".load-more",
                ".show-more",
                "[data-load-more]",
                "button[class*='load']",
                "a[class*='load']"
            ]

            for selector in load_more_selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            self.driver.execute_script("arguments[0].click();", button)
                            logger.info(f"点击了加载更多按钮: {selector}")
                            time.sleep(3)
                            return
                except:
                    continue

        except Exception as e:
            logger.error(f"点击加载更多按钮时出错: {e}")

    def _extract_from_javascript(self, page_source):
        """从JavaScript变量中提取数据"""
        try:
            # 查找可能的JavaScript数据
            js_patterns = [
                r'var\s+rankingData\s*=\s*(\[.*?\]);',
                r'window\.rankingData\s*=\s*(\[.*?\]);',
                r'"universities":\s*(\[.*?\])',
                r'"rankings":\s*(\[.*?\])',
                r'rankings:\s*(\[.*?\])',
                r'universities:\s*(\[.*?\])',
            ]

            for pattern in js_patterns:
                matches = re.findall(pattern, page_source, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        if isinstance(data, list) and len(data) > 10:
                            logger.info(f"从JavaScript中找到 {len(data)} 条数据")
                            return self._process_js_data(data)
                    except json.JSONDecodeError:
                        continue

            return []

        except Exception as e:
            logger.error(f"从JavaScript提取数据时出错: {e}")
            return []

    def _process_js_data(self, data):
        """处理JavaScript数据"""
        universities = []

        for item in data:
            if isinstance(item, dict):
                name = item.get('name') or item.get('university_name') or item.get('institution')
                rank = item.get('rank') or item.get('ranking') or item.get('position')
                country = item.get('country') or item.get('location')
                score = item.get('score') or item.get('overall_score')

                if name and rank:
                    universities.append({
                        "name": str(name).strip(),
                        "qs_ranking": int(rank),
                        "country": str(country).strip() if country else "",
                        "score": str(score) if score else "",
                        "official_website": "",
                        "college_website": ""
                    })

        return universities

    def _extract_from_text(self, soup):
        """从页面文本中提取数据"""
        try:
            # 获取所有文本
            text = soup.get_text()
            lines = [line.strip() for line in text.split('\n') if line.strip()]

            universities = []
            current_rank = 1

            for line in lines:
                # 查找包含大学名称的行
                if any(keyword in line for keyword in ['University', 'College', 'Institute']):
                    if len(line) > 10 and len(line) < 100:
                        universities.append({
                            "name": line,
                            "qs_ranking": current_rank,
                            "country": "",
                            "score": "",
                            "official_website": "",
                            "college_website": ""
                        })
                        current_rank += 1

                        if current_rank > 100:
                            break

            return universities

        except Exception as e:
            logger.error(f"从文本提取数据时出错: {e}")
            return []

def main():
    """主函数"""
    logger.info("开始爬取QS真实Top100排名数据...")

    scraper = QSRealTop100Scraper()
    universities = scraper.scrape_real_rankings()

    if universities:
        logger.info(f"成功获取 {len(universities)} 所大学的真实数据")

        # 处理数据
        processor = DataProcessor()
        logger.info("开始处理大学数据...")
        processed_universities = processor.batch_process_universities(universities, delay=0.1)

        # 保存数据
        output_file = "qs_real_top100_2025.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)

        logger.info(f"数据已保存到 {output_file}")

        # 显示前10名
        logger.info("\n前10名大学:")
        for i, uni in enumerate(processed_universities[:10]):
            logger.info(f"  {i+1}. {uni['name']} ({uni['country']}) - 排名: {uni['qs_ranking']}")

        print(f"\n✅ 成功爬取 {len(processed_universities)} 所大学的真实QS 2025排名数据")
        print(f"📁 数据已保存到: {output_file}")

    else:
        logger.error("未能获取到任何真实的排名数据")
        print("❌ 爬取失败，请检查网络连接或网站结构是否发生变化")

if __name__ == "__main__":
    main()
