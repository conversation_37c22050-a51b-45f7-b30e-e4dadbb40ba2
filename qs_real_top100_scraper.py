# -*- coding: utf-8 -*-
"""
QS真实Top100爬虫 - 专门爬取QS官网真实的2025年排名数据
支持分页加载，获取完整的Top100大学信息
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSRealTop100Scraper:
    """QS真实Top100爬虫类 - 改进版本"""

    def __init__(self):
        self.driver = None
        # 使用英文版QS网站，数据更准确
        self.base_url = "https://www.topuniversities.com/world-university-rankings"
        self.universities = []
        self.max_pages = 10  # 最多爬取10页，确保覆盖Top100

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 不使用无头模式，这样可以看到浏览器操作
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 使用当前目录的ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')

            # 检查ChromeDriver是否存在
            if os.path.exists(chromedriver_path):
                logger.info(f"使用本地ChromeDriver: {chromedriver_path}")
                # 确保ChromeDriver有执行权限
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                logger.error(f"ChromeDriver不存在: {chromedriver_path}")
                return False

            # 隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("Chrome浏览器驱动设置成功")
            return True

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_real_rankings(self):
        """爬取真实的QS排名数据 - 改进版本支持分页"""
        if not self.setup_driver():
            return []

        try:
            logger.info("正在访问QS排名页面...")
            self.driver.get(self.base_url)

            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            logger.info("页面加载完成，开始处理Cookie弹窗...")
            self._handle_cookie_popup()

            logger.info("开始爬取排名数据...")
            universities = []

            # 尝试获取当前页面的数据
            current_page_data = self._extract_current_page_data()
            if current_page_data:
                universities.extend(current_page_data)
                logger.info(f"第1页获取到 {len(current_page_data)} 所大学")

            # 如果需要更多数据，尝试分页加载
            page_num = 2
            while len(universities) < 100 and page_num <= self.max_pages:
                logger.info(f"尝试加载第 {page_num} 页...")

                if self._load_next_page(page_num):
                    time.sleep(3)  # 等待页面加载
                    next_page_data = self._extract_current_page_data()
                    if next_page_data:
                        universities.extend(next_page_data)
                        logger.info(f"第{page_num}页获取到 {len(next_page_data)} 所大学")
                    else:
                        logger.info(f"第{page_num}页没有获取到数据，停止分页")
                        break
                else:
                    logger.info(f"无法加载第{page_num}页，停止分页")
                    break

                page_num += 1

            # 去重并排序
            universities = self._deduplicate_and_sort(universities)

            logger.info(f"总共获取到 {len(universities)} 所大学的数据")
            return universities[:100]  # 只返回前100名

        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _handle_cookie_popup(self):
        """处理Cookie弹窗"""
        try:
            # 常见的Cookie接受按钮选择器
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                "button:contains('Accept')",
                "button:contains('同意')",
                "button:contains('接受')",
                ".cookie-accept",
                ".accept-cookies",
                "#accept-cookies",
                "[data-accept-cookies]"
            ]

            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info("已处理Cookie弹窗")
                            time.sleep(1)
                            return
                except:
                    continue

        except Exception as e:
            logger.debug(f"处理Cookie弹窗时出错: {e}")

    def _load_next_page(self, page_num):
        """加载下一页数据"""
        try:
            # 尝试多种分页方法

            # 方法1: 直接修改URL
            next_page_url = f"{self.base_url}?page={page_num}"
            self.driver.get(next_page_url)
            time.sleep(3)
            return True

        except Exception as e:
            logger.error(f"加载第{page_num}页失败: {e}")
            return False

    def _extract_current_page_data(self):
        """提取当前页面的排名数据"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            universities = []

            # QS网站的排名表格选择器
            table_selectors = [
                'table.ranking-table tbody tr',
                '.ranking-data tbody tr',
                '.university-list .university-item',
                '.ranking-item',
                'table tbody tr',
                '.rankings-table tbody tr'
            ]

            for selector in table_selectors:
                rows = soup.select(selector)
                if len(rows) >= 5:  # 确保找到足够的数据行
                    logger.info(f"使用选择器 '{selector}' 找到 {len(rows)} 行数据")
                    universities = self._parse_table_rows(rows)
                    if universities:
                        break

            return universities

        except Exception as e:
            logger.error(f"提取当前页面数据时出错: {e}")
            return []

    def _parse_table_rows(self, rows):
        """解析表格行数据"""
        universities = []

        for row in rows:
            try:
                university_data = self._extract_university_from_row(row)
                if university_data:
                    universities.append(university_data)
            except Exception as e:
                logger.debug(f"解析行数据时出错: {e}")
                continue

        return universities

    def _extract_university_from_row(self, row):
        """从表格行中提取大学信息"""
        try:
            # 获取所有文本内容
            text_content = row.get_text(separator='|', strip=True)
            cells = [cell.strip() for cell in text_content.split('|') if cell.strip()]

            if len(cells) < 3:
                return None

            # 提取排名
            rank = self._extract_rank_from_cells(cells)
            if not rank or rank > 1000:
                return None

            # 提取大学名称
            university_name = self._extract_name_from_cells(cells)
            if not university_name:
                return None

            # 提取国家
            country = self._extract_country_from_cells(cells)

            # 查找链接获取更多信息
            links = row.find_all('a', href=True)
            official_website = ""
            for link in links:
                href = link.get('href', '')
                if href and ('http' in href or href.startswith('/')):
                    if not href.startswith('http'):
                        href = 'https://www.topuniversities.com' + href
                    official_website = href
                    break

            return {
                "name": university_name,
                "qs_ranking": rank,
                "country": country,
                "official_website": official_website
            }

        except Exception as e:
            logger.debug(f"从行中提取大学信息时出错: {e}")
            return None

    def _deduplicate_and_sort(self, universities):
        """去重并排序"""
        seen = set()
        unique_universities = []

        for uni in universities:
            key = f"{uni['name']}_{uni['qs_ranking']}"
            if key not in seen:
                seen.add(key)
                unique_universities.append(uni)

        # 按排名排序
        unique_universities.sort(key=lambda x: x['qs_ranking'])
        return unique_universities

    def _extract_rank_from_cells(self, cells):
        """从单元格中提取排名"""
        for cell in cells:
            # 查找纯数字排名
            if re.match(r'^\d+$', cell):
                rank = int(cell)
                if 1 <= rank <= 1000:
                    return rank

            # 查找带有排名标识的数字
            rank_match = re.search(r'(\d+)', cell)
            if rank_match:
                rank = int(rank_match.group(1))
                if 1 <= rank <= 1000:
                    return rank

        return None

    def _extract_name_from_cells(self, cells):
        """从单元格中提取大学名称"""
        for cell in cells:
            # 跳过纯数字（排名）
            if re.match(r'^\d+$', cell):
                continue

            # 跳过分数
            if re.match(r'^\d+\.?\d*$', cell):
                continue

            # 跳过太短的文本
            if len(cell) < 5:
                continue

            # 查找包含大学关键词的文本
            university_keywords = ['University', 'College', 'Institute', 'School', 'Academy', 'Polytechnic']
            if any(keyword in cell for keyword in university_keywords):
                return cell

            # 如果长度合适且不是国家名，可能是大学名
            if 10 <= len(cell) <= 100:
                common_countries = ['United States', 'United Kingdom', 'China', 'Germany', 'Australia', 'Canada']
                if cell not in common_countries:
                    return cell

        return None

    def _extract_country_from_cells(self, cells):
        """从单元格中提取国家"""
        common_countries = [
            'United States', 'United Kingdom', 'China', 'Germany', 'Australia',
            'Canada', 'Japan', 'France', 'Singapore', 'Switzerland', 'Netherlands',
            'Sweden', 'Denmark', 'Norway', 'South Korea', 'Hong Kong', 'Taiwan',
            'India', 'Malaysia', 'Thailand', 'Indonesia', 'Philippines', 'Vietnam',
            'Brazil', 'Argentina', 'Chile', 'South Africa', 'Egypt', 'Israel',
            'Turkey', 'Russia', 'Italy', 'Spain', 'Belgium', 'Austria', 'Ireland'
        ]

        for cell in cells:
            for country in common_countries:
                if country.lower() in cell.lower():
                    return country

        # 如果没有找到完整匹配，查找可能的国家名
        for cell in cells:
            if len(cell) < 30 and re.match(r'^[A-Za-z\s]+$', cell):
                # 排除明显的大学名称
                if not any(keyword in cell for keyword in ['University', 'College', 'Institute']):
                    return cell

        return ""

def main():
    """主函数 - 爬取QS真实Top100排名数据"""
    logger.info("开始爬取QS真实Top100排名数据...")

    scraper = QSRealTop100Scraper()
    universities = scraper.scrape_real_rankings()

    if universities:
        logger.info(f"成功获取 {len(universities)} 所大学的真实数据")

        # 处理数据，添加洲际信息和缩写
        processor = DataProcessor()
        logger.info("开始处理大学数据...")
        processed_universities = []

        for uni in universities:
            processed_uni = {
                "name": uni['name'],
                "qs_ranking": uni['qs_ranking'],
                "country": uni['country'],
                "continent": processor.get_continent_by_country(uni['country']),
                "official_website": uni.get('official_website', ''),
            }
            processed_universities.append(processed_uni)

        # 保存数据
        output_file = "qs_real_top100_2025.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)

        logger.info(f"数据已保存到 {output_file}")

        # 显示前10名
        logger.info("\n前10名大学:")
        for uni in processed_universities[:10]:
            logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']}) - {uni['continent']}")

        print(f"\n✅ 成功爬取 {len(processed_universities)} 所大学的真实QS 2025排名数据")
        print(f"📁 数据已保存到: {output_file}")

        # 验证数据质量
        validate_data_quality(processed_universities)

    else:
        logger.error("未能获取到任何真实的排名数据")
        print("❌ 爬取失败，请检查网络连接或网站结构是否发生变化")

def validate_data_quality(universities):
    """验证数据质量"""
    logger.info("\n=== 数据质量验证 ===")

    # 检查排名连续性
    rankings = [uni['qs_ranking'] for uni in universities]
    missing_ranks = []
    for i in range(1, min(101, len(universities) + 1)):
        if i not in rankings:
            missing_ranks.append(i)

    if missing_ranks:
        logger.warning(f"缺失的排名: {missing_ranks[:10]}{'...' if len(missing_ranks) > 10 else ''}")
    else:
        logger.info("✅ 排名数据完整")

    # 检查重复数据
    unique_names = set(uni['name'] for uni in universities)
    if len(unique_names) != len(universities):
        logger.warning(f"发现重复大学名称，去重前: {len(universities)}, 去重后: {len(unique_names)}")
    else:
        logger.info("✅ 无重复数据")

    # 检查国家信息
    no_country = [uni['name'] for uni in universities if not uni['country']]
    if no_country:
        logger.warning(f"缺失国家信息的大学: {len(no_country)} 所")
    else:
        logger.info("✅ 国家信息完整")

if __name__ == "__main__":
    main()
