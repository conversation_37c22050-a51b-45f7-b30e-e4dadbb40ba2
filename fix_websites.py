# -*- coding: utf-8 -*-
"""
修复官网链接 - 将相对路径转换为真正的大学官网
"""

import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_official_websites():
    """修复官网链接"""
    
    # 读取数据
    with open('qs_top40_2025_with_websites.json', 'r', encoding='utf-8') as f:
        universities = json.load(f)
    
    logger.info(f"读取了 {len(universities)} 所大学的数据")
    
    # 知名大学的正确官网
    correct_websites = {
        'Massachusetts Institute of Technology (MIT)': 'https://web.mit.edu',
        'Imperial College London': 'https://www.imperial.ac.uk',
        'University of Oxford': 'https://www.ox.ac.uk',
        'Harvard University': 'https://www.harvard.edu',
        'University of Cambridge': 'https://www.cam.ac.uk',
        'Stanford University': 'https://www.stanford.edu',
        'ETH Zurich': 'https://ethz.ch',
        'National University of Singapore (NUS)': 'https://www.nus.edu.sg',
        'UCL': 'https://www.ucl.ac.uk',
        'California Institute of Technology (Caltech)': 'https://www.caltech.edu',
        'University of Pennsylvania': 'https://www.upenn.edu',
        'University of California, Berkeley (UCB)': 'https://www.berkeley.edu',
        'The University of Melbourne': 'https://www.unimelb.edu.au',
        'Peking University': 'https://www.pku.edu.cn',
        'Nanyang Technological University, Singapore (NTU Singapore)': 'https://www.ntu.edu.sg',
        'Cornell University': 'https://www.cornell.edu',
        'The University of Hong Kong': 'https://www.hku.hk',
        'The University of Sydney': 'https://www.sydney.edu.au',
        'The University of New South Wales (UNSW Sydney)': 'https://www.unsw.edu.au',
        'Tsinghua University': 'https://www.tsinghua.edu.cn',
        'University of Chicago': 'https://www.uchicago.edu',
        'Princeton University': 'https://www.princeton.edu',
        'Yale University': 'https://www.yale.edu',
        'Université PSL': 'https://psl.eu',
        'University of Toronto': 'https://www.utoronto.ca',
        'EPFL – École polytechnique fédérale de Lausanne': 'https://www.epfl.ch',
        'The University of Edinburgh': 'https://www.ed.ac.uk',
        'Technical University of Munich': 'https://www.tum.de',
        'McGill University': 'https://www.mcgill.ca',
        'Australian National University (ANU)': 'https://www.anu.edu.au',
        'Seoul National University': 'https://www.snu.ac.kr',
        'Johns Hopkins University': 'https://www.jhu.edu',
        'The University of Tokyo': 'https://www.u-tokyo.ac.jp',
        'Columbia University': 'https://www.columbia.edu',
        'The University of Manchester': 'https://www.manchester.ac.uk',
        'The Chinese University of Hong Kong (CUHK)': 'https://www.cuhk.edu.hk',
        'Monash University': 'https://www.monash.edu',
        'University of British Columbia': 'https://www.ubc.ca',
        'Fudan University': 'https://www.fudan.edu.cn',
        'King\'s College London': 'https://www.kcl.ac.uk'
    }
    
    # 修复官网链接
    fixed_count = 0
    for uni in universities:
        name = uni['name']
        current_website = uni['official_website']
        
        # 如果当前网站是相对路径或QS网站链接，替换为正确的官网
        if (not current_website or 
            current_website.startswith('/') or 
            'topuniversities.com' in current_website or
            'signup' in current_website or
            current_website.endswith('.pdf')):
            
            if name in correct_websites:
                uni['official_website'] = correct_websites[name]
                fixed_count += 1
                logger.info(f"修复 {name}: {correct_websites[name]}")
            else:
                # 尝试模糊匹配
                for correct_name, website in correct_websites.items():
                    if _names_similar(name, correct_name):
                        uni['official_website'] = website
                        fixed_count += 1
                        logger.info(f"模糊匹配修复 {name} -> {correct_name}: {website}")
                        break
    
    logger.info(f"修复了 {fixed_count} 所大学的官网链接")
    
    # 修复国家信息
    fix_country_info(universities)
    
    # 保存修复后的数据
    output_file = "qs_top40_2025_fixed_websites.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(universities, f, ensure_ascii=False, indent=2)
    
    logger.info(f"修复后的数据已保存到 {output_file}")
    
    # 显示修复结果
    logger.info("\n修复后的Top40大学官网:")
    valid_websites = 0
    for uni in universities:
        website = uni['official_website']
        status = "✅" if website and website.startswith('http') else "❌"
        if website and website.startswith('http'):
            valid_websites += 1
        logger.info(f"  {uni['qs_ranking']}. {uni['name']} - {website} {status}")
    
    logger.info(f"\n最终统计: {valid_websites}/{len(universities)} 所大学有有效官网 ({valid_websites/len(universities)*100:.1f}%)")
    
    return universities

def _names_similar(name1, name2):
    """检查两个大学名称是否相似"""
    import re
    
    # 提取主要词汇
    words1 = set(re.findall(r'\w+', name1.lower()))
    words2 = set(re.findall(r'\w+', name2.lower()))
    
    # 移除常见词汇
    common_words = {'university', 'college', 'institute', 'school', 'of', 'the', 'and', 'technology'}
    words1 -= common_words
    words2 -= common_words
    
    # 计算交集
    intersection = words1 & words2
    union = words1 | words2
    
    if not union:
        return False
    
    # 如果交集占比超过60%，认为相似
    similarity = len(intersection) / len(union)
    return similarity > 0.6

def fix_country_info(universities):
    """修复国家信息"""
    
    country_mapping = {
        'Massachusetts Institute of Technology (MIT)': 'United States',
        'Imperial College London': 'United Kingdom',
        'University of Oxford': 'United Kingdom',
        'Harvard University': 'United States',
        'University of Cambridge': 'United Kingdom',
        'Stanford University': 'United States',
        'ETH Zurich': 'Switzerland',
        'National University of Singapore (NUS)': 'Singapore',
        'UCL': 'United Kingdom',
        'California Institute of Technology (Caltech)': 'United States',
        'University of Pennsylvania': 'United States',
        'University of California, Berkeley (UCB)': 'United States',
        'The University of Melbourne': 'Australia',
        'Peking University': 'China',
        'Nanyang Technological University, Singapore (NTU Singapore)': 'Singapore',
        'Cornell University': 'United States',
        'The University of Hong Kong': 'Hong Kong',
        'The University of Sydney': 'Australia',
        'The University of New South Wales (UNSW Sydney)': 'Australia',
        'Tsinghua University': 'China',
        'University of Chicago': 'United States',
        'Princeton University': 'United States',
        'Yale University': 'United States',
        'Université PSL': 'France',
        'University of Toronto': 'Canada',
        'EPFL – École polytechnique fédérale de Lausanne': 'Switzerland',
        'The University of Edinburgh': 'United Kingdom',
        'Technical University of Munich': 'Germany',
        'McGill University': 'Canada',
        'Australian National University (ANU)': 'Australia',
        'Seoul National University': 'South Korea',
        'Johns Hopkins University': 'United States',
        'The University of Tokyo': 'Japan',
        'Columbia University': 'United States',
        'The University of Manchester': 'United Kingdom',
        'The Chinese University of Hong Kong (CUHK)': 'Hong Kong',
        'Monash University': 'Australia',
        'University of British Columbia': 'Canada',
        'Fudan University': 'China',
        'King\'s College London': 'United Kingdom'
    }
    
    continent_mapping = {
        'United States': 'North America',
        'Canada': 'North America',
        'United Kingdom': 'Europe',
        'Switzerland': 'Europe',
        'France': 'Europe',
        'Germany': 'Europe',
        'Singapore': 'Asia',
        'China': 'Asia',
        'Hong Kong': 'Asia',
        'Japan': 'Asia',
        'South Korea': 'Asia',
        'Australia': 'Oceania'
    }
    
    fixed_countries = 0
    for uni in universities:
        name = uni['name']
        
        # 修复国家信息
        if not uni['country'] or uni['continent'] == 'Unknown':
            if name in country_mapping:
                uni['country'] = country_mapping[name]
                uni['continent'] = continent_mapping.get(country_mapping[name], 'Unknown')
                fixed_countries += 1
    
    logger.info(f"修复了 {fixed_countries} 所大学的国家信息")

def main():
    """主函数"""
    logger.info("开始修复官网链接...")
    
    universities = fix_official_websites()
    
    print(f"\n✅ 官网链接修复完成！")
    print(f"📁 修复后数据保存到: qs_top40_2025_fixed_websites.json")
    
    # 验证第9名UCL
    ucl = next((uni for uni in universities if uni['qs_ranking'] == 9), None)
    if ucl:
        print(f"🔍 第9名验证: {ucl['name']} - {ucl['official_website']}")

if __name__ == "__main__":
    main()
