# -*- coding: utf-8 -*-
"""
QS真实数据爬虫 - 从QS官网爬取真实的2025年排名数据
绝不使用预定义数据，只从官网获取真实数据
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSRealOnlyScraper:
    """QS真实数据爬虫 - 绝不使用预定义数据"""

    def __init__(self):
        self.driver = None
        # QS官方排名页面
        self.urls = [
            "https://www.topuniversities.com/world-university-rankings",
            "https://www.qschina.cn/university-rankings/world-university-rankings/2025"
        ]

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 不使用无头模式，方便调试
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 使用本地ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            if os.path.exists(chromedriver_path):
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome驱动设置成功")
                return True
            else:
                logger.error("ChromeDriver不存在")
                return False

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_real_data_only(self):
        """只从QS官网爬取真实数据，绝不使用预定义数据"""
        if not self.setup_driver():
            logger.error("无法设置浏览器驱动")
            return []

        all_universities = []

        for url in self.urls:
            try:
                logger.info(f"正在从 {url} 爬取真实数据...")
                universities = self._scrape_from_url(url)

                if universities and len(universities) >= 20:
                    logger.info(f"从 {url} 成功获取 {len(universities)} 所大学的真实数据")
                    all_universities.extend(universities)
                    break
                else:
                    logger.warning(f"从 {url} 获取的数据不足: {len(universities)} 所")

            except Exception as e:
                logger.error(f"从 {url} 爬取失败: {e}")
                continue

        if self.driver:
            self.driver.quit()

        if not all_universities:
            logger.error("未能从任何QS官网获取到真实数据")
            return []

        # 清理数据
        clean_data = self._clean_and_validate_data(all_universities)
        logger.info(f"清理后获得 {len(clean_data)} 所大学的有效数据")

        return clean_data[:100]

    def _scrape_from_url(self, url):
        """从指定URL爬取数据"""
        try:
            logger.info(f"访问页面: {url}")
            self.driver.get(url)

            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            logger.info("页面加载完成，等待数据渲染...")
            time.sleep(10)  # 等待JavaScript渲染

            # 处理弹窗
            self._handle_popups()

            # 滚动加载数据
            self._scroll_and_wait()

            # 提取数据
            universities = self._extract_real_data()

            logger.info(f"从页面提取到 {len(universities)} 所大学")

            # 如果数据不足，尝试加载更多
            if len(universities) < 50:
                logger.info("数据不足，尝试加载更多...")
                more_universities = self._try_load_more()
                universities.extend(more_universities)

            return universities

        except Exception as e:
            logger.error(f"从 {url} 爬取数据时出错: {e}")
            return []

    def _handle_popups(self):
        """处理各种弹窗"""
        try:
            # Cookie弹窗
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                ".cookie-accept",
                "#accept-cookies",
                ".accept-all",
                "[data-accept]"
            ]

            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            element.click()
                            logger.info("处理了Cookie弹窗")
                            time.sleep(2)
                            return
                except:
                    continue

        except Exception as e:
            logger.debug(f"处理弹窗时出错: {e}")

    def _scroll_and_wait(self):
        """滚动页面并等待数据加载"""
        try:
            logger.info("开始滚动页面...")

            for i in range(15):  # 增加滚动次数
                # 滚动到页面底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)

                # 滚动到页面中间
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
                time.sleep(2)

                logger.info(f"完成第 {i+1} 次滚动")

        except Exception as e:
            logger.error(f"滚动页面时出错: {e}")

    def _extract_real_data(self):
        """提取真实的大学排名数据"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            universities = []

            # 保存页面源码用于调试
            with open('debug_page_source.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            logger.info("页面源码已保存到 debug_page_source.html")

            # 尝试多种选择器策略
            strategies = [
                self._extract_from_table,
                self._extract_from_list,
                self._extract_from_cards,
                self._extract_from_text_patterns
            ]

            for strategy in strategies:
                try:
                    universities = strategy(soup)
                    if len(universities) >= 10:
                        logger.info(f"使用策略 {strategy.__name__} 成功提取 {len(universities)} 所大学")
                        break
                    else:
                        logger.info(f"策略 {strategy.__name__} 只提取到 {len(universities)} 所大学")
                except Exception as e:
                    logger.error(f"策略 {strategy.__name__} 失败: {e}")
                    continue

            return universities

        except Exception as e:
            logger.error(f"提取数据时出错: {e}")
            return []

    def _extract_from_table(self, soup):
        """从表格中提取数据"""
        universities = []

        # 查找表格
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            if len(rows) >= 10:  # 确保是排名表格
                logger.info(f"找到包含 {len(rows)} 行的表格")

                for i, row in enumerate(rows[1:], 1):  # 跳过表头
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        university_data = self._parse_table_row(cells, i)
                        if university_data:
                            universities.append(university_data)

                if len(universities) >= 10:
                    break

        return universities

    def _extract_from_list(self, soup):
        """从列表中提取数据"""
        universities = []

        # 查找列表项
        list_selectors = [
            '.university-item',
            '.ranking-item',
            '.uni-link',
            '[data-rank]',
            '.university-card'
        ]

        for selector in list_selectors:
            items = soup.select(selector)
            if len(items) >= 10:
                logger.info(f"使用选择器 {selector} 找到 {len(items)} 个项目")

                for i, item in enumerate(items, 1):
                    university_data = self._parse_list_item(item, i)
                    if university_data:
                        universities.append(university_data)

                if len(universities) >= 10:
                    break

        return universities

    def _extract_from_cards(self, soup):
        """从卡片布局中提取数据"""
        universities = []

        # 查找卡片
        card_selectors = [
            '.card',
            '.university-card',
            '.ranking-card',
            '.institution-card'
        ]

        for selector in card_selectors:
            cards = soup.select(selector)
            if len(cards) >= 10:
                logger.info(f"使用选择器 {selector} 找到 {len(cards)} 个卡片")

                for i, card in enumerate(cards, 1):
                    university_data = self._parse_card(card, i)
                    if university_data:
                        universities.append(university_data)

                if len(universities) >= 10:
                    break

        return universities

    def _extract_from_text_patterns(self, soup):
        """从文本模式中提取数据"""
        universities = []

        # 获取所有文本
        text = soup.get_text()
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # 查找大学名称模式
        university_pattern = r'(\d+)\s*[.\-]\s*([^0-9\n]+(?:University|College|Institute|School|Academy)[^0-9\n]*)'

        matches = re.findall(university_pattern, text, re.IGNORECASE)

        for rank_str, name in matches:
            try:
                rank = int(rank_str)
                if 1 <= rank <= 1000 and len(name.strip()) > 5:
                    universities.append({
                        "name": name.strip(),
                        "qs_ranking": rank,
                        "country": "",
                        "official_website": ""
                    })
            except:
                continue

        return universities[:100]

    def _parse_table_row(self, cells, default_rank):
        """解析表格行"""
        try:
            texts = [cell.get_text(strip=True) for cell in cells]

            # 查找排名
            rank = default_rank
            for text in texts:
                if re.match(r'^\d+$', text):
                    rank = int(text)
                    break

            # 查找大学名称
            name = None
            for text in texts:
                if len(text) > 10 and any(keyword in text for keyword in ['University', 'College', 'Institute']):
                    name = text
                    break

            if name and 1 <= rank <= 1000:
                return {
                    "name": name,
                    "qs_ranking": rank,
                    "country": "",
                    "official_website": ""
                }

        except Exception as e:
            logger.debug(f"解析表格行时出错: {e}")

        return None

    def _parse_list_item(self, item, default_rank):
        """解析列表项"""
        try:
            text = item.get_text(strip=True)

            # 查找排名
            rank_match = re.search(r'(\d+)', text)
            rank = int(rank_match.group(1)) if rank_match else default_rank

            # 查找大学名称
            if any(keyword in text for keyword in ['University', 'College', 'Institute']):
                # 清理文本
                name = re.sub(r'^\d+\s*[.\-]\s*', '', text).strip()

                # 尝试从item的HTML结构中提取国家信息
                country = self._extract_country_from_element(item)

                if len(name) > 5 and 1 <= rank <= 1000:
                    return {
                        "name": name,
                        "qs_ranking": rank,
                        "country": country,
                        "official_website": ""
                    }

        except Exception as e:
            logger.debug(f"解析列表项时出错: {e}")

        return None

    def _extract_country_from_element(self, element):
        """从HTML元素中提取国家信息"""
        try:
            # 查找可能包含国家信息的子元素
            country_selectors = [
                '.country',
                '.location',
                '.flag',
                '[data-country]',
                '.university-country'
            ]

            for selector in country_selectors:
                country_elem = element.select_one(selector)
                if country_elem:
                    country_text = country_elem.get_text(strip=True)
                    if country_text:
                        return self._normalize_country_name(country_text)

            # 如果没有找到专门的国家元素，从文本中推断
            text = element.get_text()
            return self._infer_country_from_text(text)

        except Exception as e:
            logger.debug(f"提取国家信息时出错: {e}")
            return ""

    def _normalize_country_name(self, country_text):
        """标准化国家名称"""
        country_mapping = {
            'US': 'United States',
            'USA': 'United States',
            'UK': 'United Kingdom',
            'GB': 'United Kingdom',
            'CN': 'China',
            'SG': 'Singapore',
            'AU': 'Australia',
            'CA': 'Canada',
            'DE': 'Germany',
            'FR': 'France',
            'JP': 'Japan',
            'CH': 'Switzerland',
            'NL': 'Netherlands',
            'SE': 'Sweden',
            'DK': 'Denmark',
            'NO': 'Norway',
            'KR': 'South Korea',
            'HK': 'Hong Kong',
            'TW': 'Taiwan'
        }

        # 如果是缩写，转换为全名
        if country_text.upper() in country_mapping:
            return country_mapping[country_text.upper()]

        return country_text

    def _infer_country_from_text(self, text):
        """从文本中推断国家"""
        # 基于大学名称推断国家
        if 'MIT' in text or 'Harvard' in text or 'Stanford' in text or 'Caltech' in text:
            return 'United States'
        elif 'Oxford' in text or 'Cambridge' in text or 'Imperial College' in text:
            return 'United Kingdom'
        elif 'Singapore' in text:
            return 'Singapore'
        elif 'Melbourne' in text or 'Sydney' in text or 'UNSW' in text or 'ANU' in text:
            return 'Australia'
        elif 'Toronto' in text or 'McGill' in text:
            return 'Canada'
        elif 'Peking' in text or 'Tsinghua' in text:
            return 'China'
        elif 'Hong Kong' in text:
            return 'Hong Kong'
        elif 'Technical University of Munich' in text:
            return 'Germany'
        elif 'Edinburgh' in text:
            return 'United Kingdom'

        return ""

    def _parse_card(self, card, default_rank):
        """解析卡片"""
        try:
            text = card.get_text(strip=True)

            # 查找排名
            rank_match = re.search(r'(\d+)', text)
            rank = int(rank_match.group(1)) if rank_match else default_rank

            # 查找大学名称
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            name = None

            for line in lines:
                if any(keyword in line for keyword in ['University', 'College', 'Institute']):
                    name = line
                    break

            if name and 1 <= rank <= 1000:
                return {
                    "name": name,
                    "qs_ranking": rank,
                    "country": "",
                    "official_website": ""
                }

        except Exception as e:
            logger.debug(f"解析卡片时出错: {e}")

        return None

    def _try_load_more(self):
        """尝试加载更多数据"""
        universities = []

        try:
            # 尝试多种方法加载更多数据

            # 方法1: 点击加载更多按钮
            load_more_selectors = [
                "button:contains('Load More')",
                "button:contains('Show More')",
                ".load-more",
                ".show-more",
                "[data-load-more]",
                "button[class*='load']"
            ]

            for selector in load_more_selectors:
                try:
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in buttons:
                        if button.is_displayed():
                            button.click()
                            logger.info(f"点击了加载更多按钮: {selector}")
                            time.sleep(5)

                            # 提取新数据
                            new_universities = self._extract_real_data()
                            universities.extend(new_universities)
                            return universities
                except:
                    continue

            # 方法2: 尝试访问分页URL
            try:
                for page in range(2, 6):  # 尝试访问第2-5页
                    page_url = f"https://www.topuniversities.com/world-university-rankings?page={page}"
                    logger.info(f"尝试访问分页: {page_url}")

                    self.driver.get(page_url)
                    time.sleep(5)

                    page_universities = self._extract_real_data()
                    if page_universities:
                        logger.info(f"从第{page}页获取到 {len(page_universities)} 所大学")
                        universities.extend(page_universities)
                    else:
                        break

            except Exception as e:
                logger.error(f"访问分页时出错: {e}")

            # 方法3: 尝试修改URL参数获取更多数据
            try:
                more_url = "https://www.topuniversities.com/world-university-rankings?tab=indicators&sort_by=rank&order_by=asc&search=&page=0"
                logger.info(f"尝试访问完整排名页面: {more_url}")

                self.driver.get(more_url)
                time.sleep(8)
                self._scroll_and_wait()

                more_universities = self._extract_real_data()
                if more_universities:
                    logger.info(f"从完整排名页面获取到 {len(more_universities)} 所大学")
                    universities.extend(more_universities)

            except Exception as e:
                logger.error(f"访问完整排名页面时出错: {e}")

        except Exception as e:
            logger.error(f"尝试加载更多时出错: {e}")

        return universities

    def _clean_and_validate_data(self, universities):
        """清理和验证数据"""
        seen = set()
        clean_universities = []

        for uni in universities:
            # 验证数据有效性
            if not uni.get('name') or not uni.get('qs_ranking'):
                continue

            # 去重
            key = f"{uni['name']}_{uni['qs_ranking']}"
            if key in seen:
                continue
            seen.add(key)

            # 验证大学名称
            name = uni['name'].strip()
            if len(name) < 5 or name in ['View profile', 'Read more']:
                continue

            # 验证排名
            rank = uni['qs_ranking']
            if not isinstance(rank, int) or rank < 1 or rank > 1000:
                continue

            clean_universities.append(uni)

        # 按排名排序
        clean_universities.sort(key=lambda x: x['qs_ranking'])

        return clean_universities

def main():
    """主函数 - 只从QS官网爬取真实数据"""
    logger.info("=" * 60)
    logger.info("开始从QS官网爬取真实的2025年排名数据")
    logger.info("绝不使用预定义数据，只从官网获取真实数据!")
    logger.info("=" * 60)

    scraper = QSRealOnlyScraper()
    universities = scraper.scrape_real_data_only()

    if universities and len(universities) >= 10:
        logger.info(f"成功从QS官网获取 {len(universities)} 所大学的真实数据")

        # 处理数据
        processor = DataProcessor()
        processed_universities = []

        for uni in universities:
            processed_uni = {
                "name": uni['name'],
                "qs_ranking": uni['qs_ranking'],
                "country": uni['country'],
                "continent": processor.get_continent_by_country(uni['country']),
                "official_website": uni.get('official_website', ''),
            }
            processed_universities.append(processed_uni)

        # 保存数据
        output_file = "qs_real_scraped_only_2025.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)

        logger.info(f"真实数据已保存到 {output_file}")

        # 显示前20名验证数据真实性
        logger.info("\n前20名大学 (请验证是否与QS官网一致):")
        for uni in processed_universities[:20]:
            logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']})")

        print(f"\n✅ 成功从QS官网爬取 {len(processed_universities)} 所大学的真实数据")
        print(f"📁 数据已保存到: {output_file}")
        print("🔍 请检查前20名数据是否与QS官网一致")
        print("📄 页面源码已保存到 debug_page_source.html 供调试使用")

    else:
        logger.error("未能从QS官网获取到足够的真实数据")
        print("❌ 爬取失败")
        print("💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 查看 debug_page_source.html 了解页面结构")
        print("   3. 可能需要手动调整选择器")

if __name__ == "__main__":
    main()
