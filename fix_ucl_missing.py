# -*- coding: utf-8 -*-
"""
修复UCL缺失问题 - 手动添加第9名UCL并重新调整排名
"""

import json
import logging
from data_processor import DataProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_ucl_missing():
    """修复UCL缺失问题"""
    
    # 读取现有数据
    with open('qs_top100_2025_complete_final.json', 'r', encoding='utf-8') as f:
        universities = json.load(f)
    
    logger.info(f"读取了 {len(universities)} 所大学的数据")
    
    # 检查是否缺少第9名
    ninth_uni = next((uni for uni in universities if uni['qs_ranking'] == 9), None)
    if ninth_uni:
        logger.info(f"第9名已存在: {ninth_uni['name']}")
        return universities
    
    logger.info("确认第9名缺失，开始修复...")
    
    # 创建UCL数据
    ucl_data = {
        "name": "University College London (UCL)",
        "qs_ranking": 9,
        "country": "United Kingdom",
        "continent": "Europe",
        "official_website": ""
    }
    
    # 找到插入位置（第8名之后，第10名之前）
    insert_index = -1
    for i, uni in enumerate(universities):
        if uni['qs_ranking'] == 8:
            insert_index = i + 1
            break
    
    if insert_index == -1:
        # 如果没找到第8名，找第10名之前
        for i, uni in enumerate(universities):
            if uni['qs_ranking'] == 10:
                insert_index = i
                break
    
    # 插入UCL
    if insert_index != -1:
        universities.insert(insert_index, ucl_data)
        logger.info(f"已在位置 {insert_index} 插入UCL")
    else:
        # 如果找不到合适位置，直接添加到开头然后重新排序
        universities.append(ucl_data)
        logger.info("添加UCL到列表末尾，将重新排序")
    
    # 重新排序
    universities.sort(key=lambda x: x['qs_ranking'])
    
    # 验证修复结果
    logger.info("验证修复结果...")
    
    # 检查前20名
    logger.info("\n修复后的前20名:")
    for uni in universities[:20]:
        logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']})")
    
    # 特别验证第9名
    ninth_uni = next((uni for uni in universities if uni['qs_ranking'] == 9), None)
    if ninth_uni and 'UCL' in ninth_uni['name']:
        logger.info(f"\n✅ 修复成功！第9名现在是: {ninth_uni['name']}")
    else:
        logger.error(f"\n❌ 修复失败！第9名是: {ninth_uni['name'] if ninth_uni else '仍然缺失'}")
    
    # 检查排名连续性
    rankings = [uni['qs_ranking'] for uni in universities]
    missing_ranks = []
    for i in range(1, 101):
        if i not in rankings:
            missing_ranks.append(i)
    
    if missing_ranks:
        logger.warning(f"仍缺失的排名: {missing_ranks}")
    else:
        logger.info("✅ 排名1-100完整")
    
    # 保存修复后的数据
    output_file = "qs_top100_2025_ucl_fixed.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(universities, f, ensure_ascii=False, indent=2)
    
    logger.info(f"修复后的数据已保存到 {output_file}")
    
    return universities

def verify_top_10():
    """验证前10名是否正确"""
    
    # 根据QS官方排名，前10名应该是：
    expected_top_10 = [
        "Massachusetts Institute of Technology (MIT)",
        "Imperial College London", 
        "University of Oxford",
        "Harvard University",
        "University of Cambridge",
        "Stanford University",
        "ETH Zurich",
        "National University of Singapore (NUS)",
        "University College London (UCL)",  # 第9名应该是UCL
        "California Institute of Technology (Caltech)"
    ]
    
    logger.info("\n=== 验证前10名 ===")
    logger.info("期望的前10名:")
    for i, name in enumerate(expected_top_10, 1):
        logger.info(f"  {i}. {name}")

def main():
    """主函数"""
    logger.info("开始修复UCL缺失问题...")
    
    # 首先验证期望的排名
    verify_top_10()
    
    # 修复数据
    universities = fix_ucl_missing()
    
    print(f"\n✅ UCL修复完成！")
    print(f"📊 总共 {len(universities)} 所大学")
    print(f"📁 修复后数据保存到: qs_top100_2025_ucl_fixed.json")
    print(f"🔍 请检查第9名是否为UCL")

if __name__ == "__main__":
    main()
