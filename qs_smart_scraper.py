# -*- coding: utf-8 -*-
"""
QS智能爬虫 - 正确处理分页和弹窗，获取真实的Top100数据
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSSmartScraper:
    """QS智能爬虫 - 正确处理分页和弹窗"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.topuniversities.com/world-university-rankings"
        self.universities = []
        self.current_page = 1
        self.max_pages = 10  # 最多爬取10页确保覆盖Top100

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 不使用无头模式，方便观察和调试
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 禁用图片和CSS加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 使用本地ChromeDriver
            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            if os.path.exists(chromedriver_path):
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome驱动设置成功")
                return True
            else:
                logger.error("ChromeDriver不存在")
                return False

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_with_pagination(self):
        """使用分页机制爬取数据"""
        if not self.setup_driver():
            return []

        all_universities = []
        
        try:
            logger.info("开始智能爬取QS排名数据...")
            
            # 访问第一页
            logger.info(f"访问第一页: {self.base_url}")
            self.driver.get(self.base_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            logger.info("页面加载完成，处理弹窗...")
            time.sleep(5)
            
            # 处理所有弹窗
            self._handle_all_popups()
            
            # 等待数据加载
            time.sleep(3)
            
            # 爬取多页数据
            for page in range(1, self.max_pages + 1):
                logger.info(f"正在爬取第 {page} 页...")
                
                # 等待当前页面数据加载
                self._wait_for_data_load()
                
                # 提取当前页面数据
                page_universities = self._extract_page_data()
                
                if page_universities:
                    logger.info(f"第 {page} 页获取到 {len(page_universities)} 所大学")
                    all_universities.extend(page_universities)
                    
                    # 如果已经获取到足够的数据，停止
                    if len(all_universities) >= 100:
                        logger.info(f"已获取足够数据: {len(all_universities)} 所大学")
                        break
                else:
                    logger.warning(f"第 {page} 页没有获取到数据")
                
                # 尝试进入下一页
                if page < self.max_pages:
                    if not self._go_to_next_page():
                        logger.info("无法进入下一页，停止爬取")
                        break
                    time.sleep(3)
            
            # 清理数据
            clean_universities = self._clean_data(all_universities)
            logger.info(f"清理后获得 {len(clean_universities)} 所大学的有效数据")
            
            return clean_universities[:100]
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _handle_all_popups(self):
        """处理所有可能的弹窗"""
        logger.info("开始处理弹窗...")
        
        # 处理Cookie弹窗
        self._handle_cookie_popup()
        
        # 处理订阅弹窗
        self._handle_subscription_popup()
        
        # 处理广告弹窗
        self._handle_ad_popup()
        
        # 处理其他弹窗
        self._handle_other_popups()
        
        logger.info("弹窗处理完成")

    def _handle_cookie_popup(self):
        """处理Cookie弹窗"""
        try:
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                "button:contains('Accept')",
                "button:contains('Accept All')",
                "button:contains('同意')",
                "button:contains('接受')",
                ".cookie-accept",
                ".accept-cookies",
                "#accept-cookies",
                ".accept-all",
                "[data-accept]",
                "[data-accept-cookies]",
                "button[data-cy='accept-all']",
                ".onetrust-accept-btn-handler"
            ]
            
            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info(f"点击了Cookie接受按钮: {selector}")
                            time.sleep(2)
                            return
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"处理Cookie弹窗时出错: {e}")

    def _handle_subscription_popup(self):
        """处理订阅弹窗"""
        try:
            # 查找关闭按钮
            close_selectors = [
                "button[aria-label='Close']",
                "button[title='Close']",
                ".close",
                ".modal-close",
                ".popup-close",
                "[data-dismiss='modal']",
                "button:contains('×')",
                "button:contains('Close')",
                "button:contains('No thanks')",
                "button:contains('Skip')",
                ".newsletter-close"
            ]
            
            for selector in close_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info(f"关闭了订阅弹窗: {selector}")
                            time.sleep(2)
                            return
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"处理订阅弹窗时出错: {e}")

    def _handle_ad_popup(self):
        """处理广告弹窗"""
        try:
            # 按ESC键关闭弹窗
            from selenium.webdriver.common.keys import Keys
            self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
            time.sleep(1)
            
        except Exception as e:
            logger.debug(f"处理广告弹窗时出错: {e}")

    def _handle_other_popups(self):
        """处理其他弹窗"""
        try:
            # 查找并关闭任何模态框
            modal_selectors = [
                ".modal",
                ".popup",
                ".overlay",
                "[role='dialog']",
                ".dialog"
            ]
            
            for selector in modal_selectors:
                try:
                    modals = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for modal in modals:
                        if modal.is_displayed():
                            # 尝试在模态框内找到关闭按钮
                            close_buttons = modal.find_elements(By.CSS_SELECTOR, "button, .close, [aria-label='Close']")
                            for btn in close_buttons:
                                if btn.is_displayed():
                                    btn.click()
                                    logger.info("关闭了模态框")
                                    time.sleep(1)
                                    return
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"处理其他弹窗时出错: {e}")

    def _wait_for_data_load(self):
        """等待数据加载完成"""
        try:
            # 等待排名数据出现
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".uni-link, .university-item, table tbody tr"))
            )
            
            # 额外等待确保数据完全加载
            time.sleep(3)
            
        except Exception as e:
            logger.warning(f"等待数据加载时出错: {e}")

    def _extract_page_data(self):
        """提取当前页面的数据"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            universities = []
            
            # 尝试多种选择器
            selectors = [
                '.uni-link',
                '.university-item',
                'table tbody tr',
                '.ranking-item',
                '[data-university]'
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                if len(elements) >= 5:  # 确保找到足够的元素
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                    
                    for i, element in enumerate(elements):
                        university_data = self._parse_university_element(element, i + 1)
                        if university_data:
                            universities.append(university_data)
                    
                    if len(universities) >= 5:
                        break
            
            return universities
            
        except Exception as e:
            logger.error(f"提取页面数据时出错: {e}")
            return []

    def _parse_university_element(self, element, default_rank):
        """解析大学元素"""
        try:
            text = element.get_text(separator='|', strip=True)
            
            # 提取排名
            rank_match = re.search(r'(\d+)', text)
            rank = int(rank_match.group(1)) if rank_match else default_rank
            
            # 提取大学名称
            name = self._extract_university_name(text)
            
            # 推断国家
            country = self._infer_country(name, text)
            
            if name and 1 <= rank <= 1000:
                return {
                    "name": name,
                    "qs_ranking": rank,
                    "country": country,
                    "official_website": ""
                }
            
        except Exception as e:
            logger.debug(f"解析大学元素时出错: {e}")
        
        return None

    def _extract_university_name(self, text):
        """提取大学名称"""
        # 移除排名数字
        text = re.sub(r'^\d+\s*[.\-]?\s*', '', text)
        
        # 查找包含大学关键词的部分
        lines = [line.strip() for line in text.split('|') if line.strip()]
        
        for line in lines:
            if any(keyword in line for keyword in ['University', 'College', 'Institute', 'School', 'Academy']):
                if 5 <= len(line) <= 150:
                    return line.strip()
        
        # 如果没有找到关键词，返回第一个合适长度的文本
        for line in lines:
            if 10 <= len(line) <= 100:
                return line.strip()
        
        return None

    def _infer_country(self, name, text):
        """推断国家"""
        if not name:
            return ""
        
        # 基于大学名称推断
        country_patterns = {
            'United States': ['MIT', 'Harvard', 'Stanford', 'Caltech', 'University of Pennsylvania', 
                             'University of California', 'Cornell', 'Princeton', 'Yale', 'University of Chicago'],
            'United Kingdom': ['Oxford', 'Cambridge', 'Imperial College', 'University College London', 
                              'King\'s College', 'Edinburgh', 'Manchester', 'Bristol'],
            'Singapore': ['Singapore', 'NUS', 'NTU'],
            'Australia': ['Melbourne', 'Sydney', 'UNSW', 'ANU', 'Monash', 'Queensland'],
            'Canada': ['Toronto', 'McGill', 'British Columbia'],
            'China': ['Peking', 'Tsinghua', 'Fudan'],
            'Hong Kong': ['Hong Kong'],
            'Germany': ['Technical University of Munich', 'Munich'],
            'Switzerland': ['ETH Zurich', 'Zurich'],
            'Japan': ['Tokyo', 'Kyoto'],
            'France': ['Sorbonne', 'École'],
            'Netherlands': ['Amsterdam', 'Delft']
        }
        
        for country, patterns in country_patterns.items():
            if any(pattern in name for pattern in patterns):
                return country
        
        return ""

    def _go_to_next_page(self):
        """进入下一页"""
        try:
            # 方法1: 查找下一页按钮
            next_selectors = [
                "a[aria-label='Next']",
                "button[aria-label='Next']",
                ".next",
                ".pagination-next",
                "a:contains('Next')",
                "button:contains('Next')",
                ".pager-next",
                "[data-page-next]"
            ]
            
            for selector in next_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # 滚动到元素可见
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(1)
                            
                            element.click()
                            logger.info(f"点击了下一页按钮: {selector}")
                            time.sleep(3)
                            return True
                except:
                    continue
            
            # 方法2: 查找页码按钮
            try:
                next_page_num = self.current_page + 1
                page_button = self.driver.find_element(By.CSS_SELECTOR, f"a:contains('{next_page_num}'), button:contains('{next_page_num}')")
                if page_button.is_displayed():
                    page_button.click()
                    logger.info(f"点击了页码按钮: {next_page_num}")
                    self.current_page = next_page_num
                    time.sleep(3)
                    return True
            except:
                pass
            
            # 方法3: 直接修改URL
            try:
                current_url = self.driver.current_url
                if '?' in current_url:
                    next_url = f"{current_url}&page={self.current_page + 1}"
                else:
                    next_url = f"{current_url}?page={self.current_page + 1}"
                
                self.driver.get(next_url)
                logger.info(f"直接访问下一页URL: {next_url}")
                self.current_page += 1
                time.sleep(5)
                return True
            except:
                pass
            
            logger.warning("无法找到下一页按钮或链接")
            return False
            
        except Exception as e:
            logger.error(f"进入下一页时出错: {e}")
            return False

    def _clean_data(self, universities):
        """清理数据"""
        seen = set()
        clean_universities = []
        
        for uni in universities:
            if not uni.get('name'):
                continue
            
            # 去重
            key = f"{uni['name']}_{uni['qs_ranking']}"
            if key in seen:
                continue
            seen.add(key)
            
            # 验证数据
            if len(uni['name']) >= 5 and 1 <= uni['qs_ranking'] <= 1000:
                clean_universities.append(uni)
        
        # 按排名排序
        clean_universities.sort(key=lambda x: x['qs_ranking'])
        
        return clean_universities

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("开始智能爬取QS真实排名数据")
    logger.info("正确处理分页和弹窗机制")
    logger.info("=" * 60)
    
    scraper = QSSmartScraper()
    universities = scraper.scrape_with_pagination()
    
    if universities and len(universities) >= 20:
        logger.info(f"成功获取 {len(universities)} 所大学的真实数据")
        
        # 处理数据
        processor = DataProcessor()
        processed_universities = []
        
        for uni in universities:
            processed_uni = {
                "name": uni['name'],
                "qs_ranking": uni['qs_ranking'],
                "country": uni['country'],
                "continent": processor.get_continent_by_country(uni['country']),
                "official_website": uni.get('official_website', ''),
            }
            processed_universities.append(processed_uni)
        
        # 保存数据
        output_file = "qs_smart_scraped_2025.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到 {output_file}")
        
        # 显示前20名
        logger.info("\n前20名大学 (验证真实性):")
        for uni in processed_universities[:20]:
            logger.info(f"  {uni['qs_ranking']}. {uni['name']} ({uni['country']})")
        
        print(f"\n✅ 成功爬取 {len(processed_universities)} 所大学的真实QS数据")
        print(f"📁 数据已保存到: {output_file}")
        
    else:
        logger.error("未能获取到足够的真实数据")
        print("❌ 爬取失败")

if __name__ == "__main__":
    main()
