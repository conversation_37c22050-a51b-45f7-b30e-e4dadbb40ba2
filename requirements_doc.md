从QS官网：
- 网站一：https://www.qschina.cn/university-rankings/world-university-rankings/2025 
- 网站二：https://www.topuniversities.com/world-university-rankings
网站一或者网站二爬取最新QS全球Top100高校信息。信息的内容和保存格式Json如下：

[
  {
    "name": "麻省理工学院",
    "qs_ranking": 1,
    "country": "United States",
    "continent": "North America",
    "official_website": "https://mit.edu",
  },
  {
    "name": "剑桥大学",
    "abbreviation": "Cambridge"
    "qs_ranking": 5,
    "country": "United Kingdom",
    "continent": "Europe",
    "official_website": "https://www.cam.ac.uk",
  }
]
请帮我用代码实现。

注：
- QS官网的数据是分页加载的。网站一默认每页加载20条数据，网站二默认每页加载30条数据。
- 在网站滚动下滑时可能会有弹窗，请注意处理。
- 我不要生成数据，只要从QS官网爬取的真实数据！