# -*- coding: utf-8 -*-
"""
QS爬虫 - 包含官方网站信息
从QS网站获取大学排名数据和官方网站链接
"""

import json
import time
import logging
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from data_processor import DataProcessor
import os
import requests

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSWithWebsitesScraper:
    """QS爬虫 - 包含官方网站信息"""

    def __init__(self):
        self.driver = None
        self.base_url = "https://www.topuniversities.com/world-university-rankings"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            chromedriver_path = os.path.join(os.getcwd(), 'chromedriver')
            if os.path.exists(chromedriver_path):
                os.chmod(chromedriver_path, 0o755)
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome驱动设置成功")
                return True
            else:
                logger.error("ChromeDriver不存在")
                return False

        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            return False

    def scrape_with_websites(self):
        """爬取包含官网信息的Top40数据"""
        if not self.setup_driver():
            return []

        all_universities = []

        try:
            # 只爬取前2页数据（前40名）
            for page_num in range(1, 3):
                logger.info(f"正在爬取第 {page_num} 页...")

                if page_num == 1:
                    page_url = self.base_url
                else:
                    page_url = f"{self.base_url}?page={page_num}"

                self.driver.get(page_url)

                WebDriverWait(self.driver, 30).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )

                time.sleep(8)

                if page_num == 1:
                    self._handle_popups()

                # 确保数据加载
                self._ensure_data_loaded()

                # 提取当前页面数据（包含官网链接）
                page_universities = self._extract_page_with_websites(page_num)

                if page_universities:
                    logger.info(f"第 {page_num} 页获取到 {len(page_universities)} 所大学")
                    all_universities.extend(page_universities)
                else:
                    logger.warning(f"第 {page_num} 页没有获取到数据")

                if len(all_universities) >= 40:
                    break

                time.sleep(3)

            # 清理数据
            clean_universities = self._clean_data(all_universities)

            return clean_universities[:40]

        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()

    def _handle_popups(self):
        """处理弹窗"""
        try:
            cookie_selectors = [
                "button[id*='accept']",
                "button[class*='accept']",
                ".cookie-accept",
                "#accept-cookies"
            ]

            for selector in cookie_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            logger.info("处理了弹窗")
                            time.sleep(2)
                            return
                except:
                    continue

        except Exception as e:
            logger.debug(f"处理弹窗时出错: {e}")

    def _ensure_data_loaded(self):
        """确保数据完全加载"""
        try:
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".uni-link"))
            )

            # 滚动页面
            for i in range(5):
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(2)

            time.sleep(5)

        except Exception as e:
            logger.warning(f"确保数据加载时出错: {e}")

    def _extract_page_with_websites(self, page_num):
        """提取当前页面数据（包含官网）"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            universities = []

            # 获取所有.uni-link元素
            uni_links = soup.select('.uni-link')
            logger.info(f"第 {page_num} 页找到 {len(uni_links)} 个 .uni-link 元素")

            for i, link in enumerate(uni_links):
                try:
                    name = link.get_text(strip=True)
                    if not name or len(name) < 2:  # 允许UCL这样的短名称
                        continue

                    # 计算排名
                    rank = (page_num - 1) * 30 + i + 1

                    # 获取大学详情页面链接
                    detail_url = link.get('href', '')
                    if detail_url and not detail_url.startswith('http'):
                        detail_url = 'https://www.topuniversities.com' + detail_url

                    # 提取国家信息
                    country = self._extract_country_from_context(link)

                    # 获取官方网站
                    official_website = self._get_official_website(detail_url, name)

                    university_data = {
                        "name": name,
                        "qs_ranking": rank,
                        "country": country,
                        "official_website": official_website
                    }

                    universities.append(university_data)

                    # 显示进度
                    logger.info(f"  {rank}. {name} - {official_website}")

                except Exception as e:
                    logger.debug(f"解析第 {i+1} 个链接时出错: {e}")
                    continue

            return universities

        except Exception as e:
            logger.error(f"提取第 {page_num} 页数据时出错: {e}")
            return []

    def _get_official_website(self, detail_url, university_name):
        """从大学详情页面获取官方网站"""
        if not detail_url:
            return self._guess_official_website(university_name)

        try:
            logger.debug(f"正在获取 {university_name} 的官网信息...")

            # 使用requests获取详情页面
            response = self.session.get(detail_url, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')

                # 查找官方网站链接的多种方式
                website_selectors = [
                    'a[href*="www."][href*=".edu"]',
                    'a[href*="www."][href*=".ac."]',
                    'a[href*="www."][href*=".org"]',
                    'a[href*="university"]',
                    'a[href*="college"]',
                    '.website a',
                    '.official-website a',
                    '.university-website a',
                    'a[title*="website"]',
                    'a[title*="Official"]',
                    'a[aria-label*="website"]'
                ]

                for selector in website_selectors:
                    links = soup.select(selector)
                    for link in links:
                        href = link.get('href', '')
                        if href and self._is_valid_university_website(href, university_name):
                            return href

                # 如果没有找到，尝试从文本中提取
                website = self._extract_website_from_text(soup.get_text(), university_name)
                if website:
                    return website

        except Exception as e:
            logger.debug(f"获取 {university_name} 官网时出错: {e}")

        # 如果都失败了，尝试猜测
        return self._guess_official_website(university_name)

    def _is_valid_university_website(self, url, university_name):
        """验证是否是有效的大学官网"""
        if not url:
            return False

        # 排除一些明显不是官网的链接
        exclude_patterns = [
            'facebook.com', 'twitter.com', 'linkedin.com', 'youtube.com',
            'instagram.com', 'topuniversities.com', 'qs.com',
            'wikipedia.org', 'google.com', 'mailto:', 'tel:'
        ]

        for pattern in exclude_patterns:
            if pattern in url.lower():
                return False

        # 检查是否包含教育相关域名
        edu_patterns = ['.edu', '.ac.', '.university', '.college']
        for pattern in edu_patterns:
            if pattern in url.lower():
                return True

        # 检查是否包含大学名称的关键词
        name_words = re.findall(r'\w+', university_name.lower())
        url_lower = url.lower()

        # 如果URL中包含大学名称的主要词汇，可能是官网
        main_words = [word for word in name_words if len(word) > 3 and word not in ['university', 'college', 'institute', 'school']]
        if main_words and any(word in url_lower for word in main_words[:2]):
            return True

        return False

    def _extract_website_from_text(self, text, university_name):
        """从文本中提取网站URL"""
        # 查找URL模式
        url_pattern = r'https?://[^\s<>"\']+|www\.[^\s<>"\']+\.[a-z]{2,}'
        urls = re.findall(url_pattern, text, re.IGNORECASE)

        for url in urls:
            if self._is_valid_university_website(url, university_name):
                if not url.startswith('http'):
                    url = 'https://' + url
                return url

        return ""

    def _guess_official_website(self, university_name):
        """基于大学名称猜测官方网站"""
        # 预定义的知名大学官网
        known_websites = {
            'Massachusetts Institute of Technology (MIT)': 'https://web.mit.edu',
            'MIT': 'https://web.mit.edu',
            'Harvard University': 'https://www.harvard.edu',
            'Stanford University': 'https://www.stanford.edu',
            'University of Oxford': 'https://www.ox.ac.uk',
            'University of Cambridge': 'https://www.cam.ac.uk',
            'Imperial College London': 'https://www.imperial.ac.uk',
            'UCL': 'https://www.ucl.ac.uk',
            'University College London': 'https://www.ucl.ac.uk',
            'ETH Zurich': 'https://ethz.ch',
            'National University of Singapore (NUS)': 'https://www.nus.edu.sg',
            'California Institute of Technology (Caltech)': 'https://www.caltech.edu',
            'University of Pennsylvania': 'https://www.upenn.edu',
            'University of California, Berkeley (UCB)': 'https://www.berkeley.edu',
            'The University of Melbourne': 'https://www.unimelb.edu.au',
            'Peking University': 'https://www.pku.edu.cn',
            'Nanyang Technological University, Singapore (NTU Singapore)': 'https://www.ntu.edu.sg',
            'Cornell University': 'https://www.cornell.edu',
            'The University of Hong Kong': 'https://www.hku.hk',
            'The University of Sydney': 'https://www.sydney.edu.au',
            'Tsinghua University': 'https://www.tsinghua.edu.cn',
            'University of Chicago': 'https://www.uchicago.edu',
            'Princeton University': 'https://www.princeton.edu',
            'Yale University': 'https://www.yale.edu',
            'University of Toronto': 'https://www.utoronto.ca',
            'The University of Edinburgh': 'https://www.ed.ac.uk',
            'Technical University of Munich': 'https://www.tum.de',
            'McGill University': 'https://www.mcgill.ca',
            'Australian National University (ANU)': 'https://www.anu.edu.au'
        }

        # 直接匹配
        if university_name in known_websites:
            return known_websites[university_name]

        # 模糊匹配
        for known_name, website in known_websites.items():
            if self._names_similar(university_name, known_name):
                return website

        return ""

    def _names_similar(self, name1, name2):
        """检查两个大学名称是否相似"""
        # 提取主要词汇
        words1 = set(re.findall(r'\w+', name1.lower()))
        words2 = set(re.findall(r'\w+', name2.lower()))

        # 移除常见词汇
        common_words = {'university', 'college', 'institute', 'school', 'of', 'the', 'and'}
        words1 -= common_words
        words2 -= common_words

        # 计算交集
        intersection = words1 & words2
        union = words1 | words2

        if not union:
            return False

        # 如果交集占比超过50%，认为相似
        similarity = len(intersection) / len(union)
        return similarity > 0.5

    def _extract_country_from_context(self, element):
        """从元素上下文中提取国家信息"""
        try:
            parent = element.parent
            if parent:
                parent_text = parent.get_text()
                return self._infer_country_from_text(parent_text)
            return ""
        except Exception as e:
            logger.debug(f"提取国家信息时出错: {e}")
            return ""

    def _infer_country_from_text(self, text):
        """从文本中推断国家"""
        country_patterns = {
            'United States': ['MIT', 'Harvard', 'Stanford', 'Caltech', 'University of California', 'Yale', 'Princeton'],
            'United Kingdom': ['Oxford', 'Cambridge', 'Imperial College', 'University College London', 'UCL', 'King\'s College'],
            'Singapore': ['Singapore', 'NUS', 'NTU'],
            'Australia': ['Melbourne', 'Sydney', 'UNSW', 'ANU', 'Monash'],
            'Canada': ['Toronto', 'McGill', 'British Columbia'],
            'China': ['Peking', 'Tsinghua', 'Fudan'],
            'Hong Kong': ['Hong Kong'],
            'Germany': ['Technical University of Munich', 'Munich'],
            'Switzerland': ['ETH Zurich', 'Zurich'],
            'Japan': ['Tokyo', 'Kyoto'],
            'France': ['Sorbonne', 'École', 'PSL'],
            'Netherlands': ['Amsterdam', 'Delft']
        }

        for country, patterns in country_patterns.items():
            if any(pattern in text for pattern in patterns):
                return country

        return ""

    def _clean_data(self, universities):
        """清理数据"""
        seen_names = set()
        clean_universities = []

        for uni in universities:
            if not uni.get('name'):
                continue

            name = uni['name'].strip()

            # 去重
            if name in seen_names:
                continue
            seen_names.add(name)

            # 验证数据有效性
            if len(name) >= 2 and 1 <= uni['qs_ranking'] <= 1000:
                clean_universities.append(uni)

        # 按排名排序
        clean_universities.sort(key=lambda x: x['qs_ranking'])

        return clean_universities

def main():
    """主函数"""
    logger.info("开始爬取包含官网信息的QS Top40排名数据...")

    scraper = QSWithWebsitesScraper()
    universities = scraper.scrape_with_websites()

    if universities and len(universities) >= 30:
        logger.info(f"成功获取 {len(universities)} 所大学的数据")

        # 处理数据
        processor = DataProcessor()
        processed_universities = []

        for uni in universities:
            processed_uni = {
                "name": uni['name'],
                "qs_ranking": uni['qs_ranking'],
                "country": uni['country'],
                "continent": processor.get_continent_by_country(uni['country']),
                "official_website": uni.get('official_website', ''),
            }
            processed_universities.append(processed_uni)

        # 保存数据
        output_file = "qs_top40_2025_with_websites.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)

        logger.info(f"数据已保存到 {output_file}")

        # 验证所有大学，特别关注官网信息
        logger.info("\nTop40大学（验证官网信息）:")
        for uni in processed_universities:
            website_status = "✅" if uni['official_website'] else "❌"
            logger.info(f"  {uni['qs_ranking']}. {uni['name']} - {uni['official_website']} {website_status}")

        # 统计官网获取情况
        with_website = sum(1 for uni in processed_universities if uni['official_website'])
        logger.info(f"\n官网获取统计: {with_website}/{len(processed_universities)} ({with_website/len(processed_universities)*100:.1f}%)")

        print(f"\n✅ 爬取完成！获取了 {len(processed_universities)} 所大学的数据")
        print(f"📁 数据已保存到: {output_file}")
        print(f"🌐 官网信息: {with_website}/{len(processed_universities)} 所大学有官网")

    else:
        logger.error(f"爬取失败，只获取到 {len(universities) if universities else 0} 所大学")
        print("❌ 爬取失败")

if __name__ == "__main__":
    main()
